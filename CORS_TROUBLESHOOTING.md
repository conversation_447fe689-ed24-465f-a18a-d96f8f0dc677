# 🔧 دليل حل مشاكل CORS و NetworkError

## 🎯 المشاكل الشائعة والحلول

### **1. NetworkError when attempting to fetch resource**

#### **السبب:**
- قيود CORS من الخادم المستهدف
- حجب الطلبات من المتصفح
- مشاكل في الشبكة

#### **الحلول المطبقة:**

##### **أ) CORS Proxy متعدد:**
```typescript
const CORS_PROXIES = [
  'https://api.allorigins.win/raw?url=',
  'https://corsproxy.io/?',
  'https://cors-anywhere.herokuapp.com/',
  'https://thingproxy.freeboard.io/fetch/',
];
```

##### **ب) إعادة المحاولة التلقائية:**
```typescript
export async function fetchWithCORS(url: string, options: FetchOptions = {}) {
  const { timeout = 10000, retries = 2, retryDelay = 1000 } = options;
  
  // جرب الطلب المباشر أولاً
  // ثم جرب كل proxy
  // أعد المحاولة عند الفشل
}
```

##### **ج) معالجة أخطاء محسنة:**
```typescript
export function getErrorMessage(error: unknown): string {
  if (error instanceof NetworkError) return error.message;
  
  if (error instanceof Error) {
    if (error.message.includes('CORS')) {
      return 'خطأ في سياسة CORS. جرب رابط آخر أو استخدم VPN.';
    }
    // المزيد من معالجة الأخطاء...
  }
}
```

---

## 🛠️ أدوات التشخيص

### **1. اختبار الاتصال المدمج:**
- اذهب إلى الإعدادات → اختبار الاتصال
- اختبر روابط مختلفة
- شاهد تفاصيل الأخطاء

### **2. وحدة التحكم (Console):**
```javascript
// افتح Developer Tools (F12)
// تحقق من رسائل الخطأ في Console
// ابحث عن:
// - CORS errors
// - Network timeouts
// - 404/403/401 errors
```

---

## 🔍 خطوات استكشاف الأخطاء

### **الخطوة 1: تحقق من الرابط**
```bash
# اختبر الرابط في المتصفح مباشرة
https://your-iptv-server.com/playlist.m3u8

# تأكد من:
# ✅ الرابط يعمل
# ✅ يحتوي على #EXTM3U
# ✅ لا يتطلب مصادقة خاصة
```

### **الخطوة 2: اختبر مع CORS Proxy**
```javascript
// في وحدة التحكم:
fetch('https://api.allorigins.win/raw?url=' + encodeURIComponent('YOUR_URL'))
  .then(r => r.text())
  .then(console.log)
  .catch(console.error);
```

### **الخطوة 3: تحقق من إعدادات الشبكة**
- جرب VPN مختلف
- تحقق من Firewall
- جرب شبكة أخرى

---

## 🌐 حلول بديلة

### **1. استخدام VPN:**
```
# VPN مجانية موصى بها:
- ProtonVPN
- Windscribe
- TunnelBear
```

### **2. تحويل الروابط:**
```javascript
// بدلاً من:
http://server.com/playlist.m3u8

// جرب:
https://api.allorigins.win/raw?url=http://server.com/playlist.m3u8
```

### **3. روابط اختبار مجانية:**
```
# روابط تعمل بدون CORS:
https://iptv-org.github.io/iptv/index.m3u
https://raw.githubusercontent.com/Free-TV/IPTV/master/playlist.m3u8
```

---

## 🔧 إعدادات متقدمة

### **1. تخصيص Timeout:**
```typescript
const response = await fetchWithCORS(url, {
  timeout: 30000, // 30 ثانية
  retries: 3,     // 3 محاولات
  retryDelay: 2000 // انتظار ثانيتين بين المحاولات
});
```

### **2. Headers مخصصة:**
```typescript
const response = await fetchWithCORS(url, {
  headers: {
    'User-Agent': 'VLC/3.0.0',
    'Referer': 'https://your-site.com'
  }
});
```

---

## 📊 رموز الأخطاء الشائعة

| الكود | المعنى | الحل |
|-------|--------|------|
| **0** | CORS/Network | استخدم proxy أو VPN |
| **401** | غير مصرح | تحقق من username/password |
| **403** | ممنوع | تحقق من الصلاحيات |
| **404** | غير موجود | تحقق من الرابط |
| **408** | انتهت المهلة | زد timeout أو حسن الاتصال |
| **500** | خطأ خادم | جرب لاحقاً أو رابط آخر |

---

## 🎯 نصائح للحصول على أفضل النتائج

### **1. اختيار مصادر موثوقة:**
- استخدم مقدمي IPTV معروفين
- تجنب الروابط المجانية غير المستقرة
- تحقق من تقييمات المستخدمين

### **2. تحسين الشبكة:**
- استخدم اتصال سلكي بدلاً من WiFi
- أغلق التطبيقات الأخرى المستهلكة للإنترنت
- جرب DNS مختلف (*******, *******)

### **3. إعدادات المتصفح:**
- فعل JavaScript
- امسح cache والcookies
- جرب متصفح آخر

---

## 🆘 إذا لم تنجح الحلول

### **اتصل بالدعم مع هذه المعلومات:**
1. **رسالة الخطأ الكاملة**
2. **نوع IPTV المستخدم** (M3U/Xtream/Stalker)
3. **الرابط المستخدم** (بدون بيانات حساسة)
4. **نتائج اختبار الاتصال**
5. **معلومات المتصفح والنظام**

### **معلومات تقنية للمطورين:**
```javascript
// للحصول على معلومات النظام:
console.log({
  userAgent: navigator.userAgent,
  platform: navigator.platform,
  language: navigator.language,
  cookieEnabled: navigator.cookieEnabled,
  onLine: navigator.onLine
});
```

---

**تذكر: معظم مشاكل IPTV تحل باستخدام VPN أو proxy مناسب! 🔧✨**
