# 🎬 دليل حل مشاكل تشغيل الفيديو في IPTV

## 🎯 المشاكل الشائعة والحلول

### **1. "تنسيق الفيديو غير مدعوم"**

#### **السبب:**
- المتصفح لا يدعم تنسيق البث
- رابط البث يستخدم بروتوكول غير مدعوم
- مشكلة في codec الفيديو

#### **الحلول:**

##### **أ) تحقق من نوع التنسيق:**
```
✅ مدعوم بالكامل:
- HLS (.m3u8) - الأفضل للـ IPTV
- MP4 - ملفات فيديو عادية
- WebM - تنسيق ويب

⚠️ دعم محدود:
- DASH (.mpd) - يحتاج مكتبة إضافية

❌ غير مدعوم:
- RTMP - بروتوكول قديم
- RTSP - للكاميرات الأمنية
- FLV - تنسيق قديم
```

##### **ب) استخدم المتصفح المناسب:**
```
🥇 الأفضل: Chrome, Firefox
🥈 جيد: Edge, Safari
❌ تجنب: Internet Explorer
```

##### **ج) جرب روابط بديلة:**
```
بدلاً من: rtmp://server.com/stream
جرب: https://server.com/stream.m3u8

بدلاً من: rtsp://camera.com/feed
جرب: https://camera.com/hls/feed.m3u8
```

---

## 🔧 الحلول المطبقة في التطبيق

### **1. مشغل IPTV محسن:**
- **دعم HLS.js** للتنسيقات المتقدمة
- **كشف تلقائي** لنوع البث
- **معالجة أخطاء ذكية** مع رسائل واضحة
- **إعادة محاولة تلقائية** عند الفشل

### **2. تشخيص التنسيقات:**
- فحص دعم المتصفح للتنسيقات المختلفة
- عرض معلومات مفصلة عن كل تنسيق
- نصائح محددة لكل حالة

### **3. معالجة أخطاء متقدمة:**
```typescript
// مثال على معالجة الأخطاء
switch (errorCode) {
  case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
    return `تنسيق ${streamType} غير مدعوم في هذا المتصفح`;
  case MediaError.MEDIA_ERR_NETWORK:
    return 'خطأ في الشبكة. جرب VPN';
  // المزيد...
}
```

---

## 🛠️ خطوات استكشاف الأخطاء

### **الخطوة 1: تحقق من دعم التنسيقات**
1. اذهب إلى **الإعدادات → دعم التنسيقات**
2. تحقق من حالة التنسيق المطلوب
3. اتبع التوصيات المعروضة

### **الخطوة 2: اختبر الرابط**
1. اذهب إلى **الإعدادات → اختبار الاتصال**
2. ضع رابط البث
3. تحقق من النتيجة

### **الخطوة 3: جرب حلول بديلة**
```bash
# إذا كان الرابط:
rtmp://server.com/live/stream

# جرب البحث عن:
https://server.com/hls/stream.m3u8
https://server.com/dash/stream.mpd
https://server.com/stream.mp4
```

### **الخطوة 4: تحسين المتصفح**
```javascript
// في وحدة التحكم (F12):
console.log('HLS Support:', Hls.isSupported());
console.log('Native HLS:', video.canPlayType('application/vnd.apple.mpegurl'));
console.log('MP4 Support:', video.canPlayType('video/mp4'));
```

---

## 📱 حلول حسب نوع البث

### **HLS (M3U8) - الأكثر شيوعاً**
```
✅ الحل:
- مدعوم بالكامل في التطبيق
- يعمل في جميع المتصفحات الحديثة
- أفضل خيار للـ IPTV

🔧 إذا لم يعمل:
- تحقق من الاتصال
- جرب VPN
- تأكد من صحة الرابط
```

### **RTMP - بروتوكول قديم**
```
❌ المشكلة:
- غير مدعوم في المتصفحات الحديثة
- يحتاج Flash Player (متوقف)

✅ الحل:
- ابحث عن رابط HLS بديل
- اطلب من مقدم الخدمة رابط M3U8
- استخدم تطبيق خارجي مثل VLC
```

### **RTSP - للكاميرات**
```
❌ المشكلة:
- مخصص للكاميرات الأمنية
- غير مدعوم في المتصفحات

✅ الحل:
- استخدم تطبيق مخصص للكاميرات
- ابحث عن رابط HTTP بديل
- استخدم برنامج مثل OBS
```

### **DASH (MPD)**
```
⚠️ المشكلة:
- يحتاج مكتبة dash.js
- غير مطبق حالياً

✅ الحل المستقبلي:
- سيتم إضافة الدعم لاحقاً
- جرب رابط HLS بديل حالياً
```

---

## 🎮 نصائح للحصول على أفضل تجربة

### **1. اختيار الروابط:**
```
🥇 الأولوية الأولى: .m3u8 (HLS)
🥈 الأولوية الثانية: .mp4 (مباشر)
🥉 الأولوية الثالثة: .webm
❌ تجنب: rtmp://, rtsp://, .flv
```

### **2. إعدادات المتصفح:**
- فعل JavaScript
- فعل autoplay للموقع
- امسح cache إذا واجهت مشاكل
- تحديث المتصفح لآخر إصدار

### **3. إعدادات الشبكة:**
- استخدم اتصال سلكي للبث عالي الجودة
- جرب VPN إذا كان البث محجوب
- تحقق من سرعة الإنترنت (5+ Mbps للـ HD)

---

## 🆘 حلول سريعة للمشاكل الشائعة

### **"الفيديو لا يظهر"**
```
1. تحقق من حالة الشبكة (أعلى الشاشة)
2. جرب إعادة تحميل الصفحة (F5)
3. تحقق من وحدة التحكم (F12) للأخطاء
4. جرب متصفح آخر
```

### **"الصوت بدون صورة"**
```
1. مشكلة في codec الفيديو
2. جرب رابط آخر
3. تحقق من إعدادات المتصفح
4. قد يكون البث صوتي فقط
```

### **"تقطع في البث"**
```
1. مشكلة في سرعة الإنترنت
2. جرب جودة أقل إذا متاحة
3. أغلق التطبيقات الأخرى
4. جرب في وقت آخر
```

### **"البث بطيء في البدء"**
```
1. طبيعي للبث المباشر
2. انتظر 10-30 ثانية
3. تحقق من سرعة الإنترنت
4. جرب خادم آخر
```

---

## 🔍 أدوات التشخيص المدمجة

### **1. فحص دعم التنسيقات:**
- الإعدادات → دعم التنسيقات
- يعرض ما يدعمه متصفحك
- نصائح محددة لكل تنسيق

### **2. اختبار الاتصال:**
- الإعدادات → اختبار الاتصال
- يختبر الوصول للرابط
- يعرض تفاصيل الأخطاء

### **3. معلومات البث:**
- تظهر في مشغل الفيديو
- نوع البث (HLS, MP4, إلخ)
- حالة البث (مباشر/مسجل)

---

## 📞 الحصول على المساعدة

### **معلومات مطلوبة للدعم:**
1. **نوع البث** (HLS, RTMP, إلخ)
2. **رسالة الخطأ الكاملة**
3. **المتصفح المستخدم**
4. **نتيجة اختبار الاتصال**
5. **لقطة شاشة من وحدة التحكم (F12)**

### **خطوات سريعة قبل طلب المساعدة:**
1. جرب رابط اختبار مجاني
2. تحقق من دعم التنسيقات
3. جرب متصفح آخر
4. جرب VPN

---

**الآن التطبيق يدعم معظم تنسيقات IPTV الشائعة! 🎉📺**

*للوصول للأدوات: http://localhost:8080/ → الإعدادات*
