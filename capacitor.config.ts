import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'app.lovable.stellar-stream-pro',
  appName: 'Stellar Stream Pro',
  webDir: 'dist',
  server: {
    androidScheme: 'https',
    url: 'https://67846032-4d93-49ff-bd52-2e8f69225f81.lovableproject.com?forceHideBadge=true',
    cleartext: true
  },
  plugins: {
    CapacitorHttp: {
      enabled: true
    }
  }
};

export default config;