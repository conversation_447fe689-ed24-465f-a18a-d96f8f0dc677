import React, { useState } from 'react';
import { VideoPlayer } from './VideoPlayer';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { X, Info, Heart, Share2 } from 'lucide-react';
import { Channel } from '@/hooks/useIPTVManager';

interface ChannelPlayerProps {
  channel: Channel;
  onClose: () => void;
}

export function ChannelPlayer({ channel, onClose }: ChannelPlayerProps) {
  const [isFavorite, setIsFavorite] = useState(false);

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: channel.name,
          text: `شاهد ${channel.name} على تطبيق IPTV`,
          url: window.location.href
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    // Here you would typically save to favorites in your state management
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl bg-background rounded-lg overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-3">
            {channel.logo && (
              <img 
                src={channel.logo} 
                alt={channel.name}
                className="w-8 h-8 rounded object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            )}
            <div>
              <h2 className="text-lg font-semibold">{channel.name}</h2>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary">{channel.category}</Badge>
                {channel.isAdult && (
                  <Badge variant="destructive">18+</Badge>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleFavorite}
              className={isFavorite ? "text-red-500" : ""}
            >
              <Heart className={`h-4 w-4 ${isFavorite ? 'fill-current' : ''}`} />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleShare}
            >
              <Share2 className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Video Player */}
        <div className="aspect-video">
          <VideoPlayer
            src={channel.url}
            title={channel.name}
            poster={channel.logo}
            autoPlay={true}
            className="w-full h-full"
          />
        </div>

        {/* Channel Info */}
        <div className="p-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Info className="h-5 w-5" />
                <span>معلومات القناة</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">الاسم:</span>
                  <span className="ml-2">{channel.name}</span>
                </div>
                <div>
                  <span className="font-medium">الفئة:</span>
                  <span className="ml-2">{channel.category}</span>
                </div>
                {channel.epg && (
                  <div>
                    <span className="font-medium">EPG ID:</span>
                    <span className="ml-2">{channel.epg}</span>
                  </div>
                )}
                <div>
                  <span className="font-medium">النوع:</span>
                  <span className="ml-2">بث مباشر</span>
                </div>
              </div>
              
              {/* URL Info (for debugging) */}
              <div className="pt-2 border-t">
                <span className="font-medium text-xs text-muted-foreground">رابط البث:</span>
                <p className="text-xs text-muted-foreground break-all mt-1">
                  {channel.url}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Controls */}
          <div className="flex justify-center space-x-2">
            <Button variant="outline" onClick={onClose}>
              إغلاق
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
