import React, { useState } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { <PERSON><PERSON><PERSON>cle, XCircle, Loader2, Wifi, WifiOff } from 'lucide-react';
import { testUrl, normalizeUrl, getErrorMessage } from '@/utils/corsProxy';

interface TestResult {
  url: string;
  status: 'testing' | 'success' | 'error';
  message: string;
  timestamp: Date;
}

export function ConnectionTest() {
  const [testUrl_input, setTestUrl] = useState('');
  const [results, setResults] = useState<TestResult[]>([]);
  const [isTestingAll, setIsTestingAll] = useState(false);

  // Common test URLs
  const testUrls = [
    'https://iptv-org.github.io/iptv/index.m3u',
    'https://raw.githubusercontent.com/Free-TV/IPTV/master/playlist.m3u8',
    'https://www.google.com',
    'https://httpbin.org/get'
  ];

  const runSingleTest = async (url: string) => {
    const normalizedUrl = normalizeUrl(url);

    // Add to results with testing status
    const testResult: TestResult = {
      url: normalizedUrl,
      status: 'testing',
      message: 'جاري الاختبار...',
      timestamp: new Date()
    };

    setResults(prev => [testResult, ...prev.filter(r => r.url !== normalizedUrl)]);

    try {
      const isAccessible = await testUrl(normalizedUrl);

      setResults(prev => prev.map(r =>
        r.url === normalizedUrl
          ? {
              ...r,
              status: isAccessible ? 'success' : 'error',
              message: isAccessible ? 'الاتصال ناجح ✓' : 'فشل في الاتصال',
              timestamp: new Date()
            }
          : r
      ));
    } catch (error) {
      setResults(prev => prev.map(r =>
        r.url === normalizedUrl
          ? {
              ...r,
              status: 'error',
              message: getErrorMessage(error),
              timestamp: new Date()
            }
          : r
      ));
    }
  };

  const runAllTests = async () => {
    setIsTestingAll(true);
    setResults([]);

    for (const url of testUrls) {
      await runSingleTest(url);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsTestingAll(false);
  };

  const handleCustomTest = () => {
    if (testUrl_input.trim()) {
      runSingleTest(testUrl_input.trim());
      setTestUrl('');
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'testing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'testing':
        return <Badge variant="secondary">جاري الاختبار</Badge>;
      case 'success':
        return <Badge variant="default" className="bg-green-500">نجح</Badge>;
      case 'error':
        return <Badge variant="destructive">فشل</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Wifi className="h-5 w-5" />
            <span>اختبار الاتصال</span>
          </CardTitle>
          <CardDescription>
            اختبر الاتصال بروابط IPTV والتحقق من مشاكل CORS
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Custom URL Test */}
          <div className="flex space-x-2">
            <Input
              placeholder="أدخل رابط للاختبار..."
              value={testUrl_input}
              onChange={(e) => setTestUrl(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleCustomTest()}
            />
            <Button onClick={handleCustomTest} disabled={!testUrl_input.trim()}>
              اختبار
            </Button>
          </div>

          {/* Quick Tests */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              onClick={runAllTests}
              disabled={isTestingAll}
            >
              {isTestingAll ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Wifi className="h-4 w-4 mr-2" />
              )}
              اختبار سريع
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>نتائج الاختبار</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {results.map((result, index) => (
                <div
                  key={`${result.url}-${index}`}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center space-x-3 flex-1">
                    {getStatusIcon(result.status)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {result.url}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {result.message}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(result.status)}
                    <span className="text-xs text-muted-foreground">
                      {result.timestamp.toLocaleTimeString('ar')}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <WifiOff className="h-5 w-5" />
            <span>حل مشاكل الاتصال</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm">
          <div>
            <h4 className="font-medium">إذا فشل الاختبار:</h4>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground mt-2">
              <li>تحقق من اتصال الإنترنت</li>
              <li><strong>جرب استخدام VPN</strong> - اذهب لتبويب "VPN Helper"</li>
              <li>تأكد من صحة الرابط</li>
              <li>بعض الخوادم قد تحجب الطلبات من المتصفح</li>
              <li>جرب في وقت آخر (قد يكون الخادم مشغول)</li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium">أخطاء CORS الشائعة:</h4>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground mt-2">
              <li>استخدم روابط تدعم CORS</li>
              <li>جرب proxy مختلف</li>
              <li>تحقق من إعدادات الخادم</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
