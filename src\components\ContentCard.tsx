import { Play, <PERSON>, Star } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { cn } from "@/lib/utils";

interface ContentCardProps {
  title: string;
  subtitle?: string;
  image: string;
  duration?: string;
  rating?: number;
  year?: string;
  type?: "movie" | "series" | "live";
  size?: "small" | "medium" | "large";
  className?: string;
  url?: string;
  onPlay?: () => void;
}

export function ContentCard({
  title,
  subtitle,
  image,
  duration,
  rating,
  year,
  type = "movie",
  size = "medium",
  className,
  url,
  onPlay,
}: ContentCardProps) {
  const sizeClasses = {
    small: "w-32 h-48",
    medium: "w-40 h-60",
    large: "w-48 h-72",
  };

  return (
    <div className={cn("group relative overflow-hidden rounded-lg transition-all duration-300 hover:scale-105", sizeClasses[size], className)}>
      {/* Image */}
      <div className="relative h-full">
        <img
          src={image}
          alt={title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Play Button */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Button
            variant="glassy"
            size="icon"
            className="h-12 w-12 rounded-full"
            onClick={onPlay}
          >
            <Play className="h-6 w-6" />
          </Button>
        </div>

        {/* Type Badge */}
        {type === "live" && (
          <div className="absolute top-2 left-2">
            <div className="flex items-center px-2 py-1 rounded-full bg-red-500 text-white text-xs font-medium">
              <div className="w-2 h-2 bg-white rounded-full mr-1 animate-pulse" />
              LIVE
            </div>
          </div>
        )}

        {/* Rating */}
        {rating && (
          <div className="absolute top-2 right-2">
            <div className="flex items-center px-2 py-1 rounded-full bg-black/50 backdrop-blur-sm text-white text-xs">
              <Star className="h-3 w-3 text-yellow-400 mr-1" />
              {rating}
            </div>
          </div>
        )}

        {/* Content Info */}
        <div className="absolute bottom-0 left-0 right-0 p-3 text-white transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
          <h3 className="font-semibold text-sm leading-tight line-clamp-2">{title}</h3>
          {subtitle && (
            <p className="text-xs text-white/80 mt-1 line-clamp-1">{subtitle}</p>
          )}

          <div className="flex items-center justify-between mt-2 text-xs text-white/70">
            {year && <span>{year}</span>}
            {duration && (
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {duration}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}