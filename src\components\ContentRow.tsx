import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "./ui/button";
import { ContentCard } from "./ContentCard";
import { cn } from "@/lib/utils";

interface ContentItem {
  id: string;
  title: string;
  subtitle?: string;
  image: string;
  duration?: string;
  rating?: number;
  year?: string;
  type?: "movie" | "series" | "live";
}

interface ContentRowProps {
  title: string;
  items: ContentItem[];
  size?: "small" | "medium" | "large";
  className?: string;
}

export function ContentRow({ title, items, size = "medium", className }: ContentRowProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {/* Section Title */}
      <div className="flex items-center justify-between px-4">
        <h2 className="text-xl font-bold text-foreground">{title}</h2>
        <Button variant="ghost" size="sm" className="text-primary">
          عرض الكل
        </Button>
      </div>

      {/* Content Carousel */}
      <div className="relative">
        {/* Navigation Buttons */}
        <Button
          variant="glassy"
          size="icon"
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        <Button
          variant="glassy"
          size="icon"
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>

        {/* Scrollable Content */}
        <div className="overflow-x-auto scrollbar-hide">
          <div className="flex space-x-4 px-4 pb-2">
            {items.map((item) => (
              <ContentCard
                key={item.id}
                title={item.title}
                subtitle={item.subtitle}
                image={item.image}
                duration={item.duration}
                rating={item.rating}
                year={item.year}
                type={item.type}
                size={size}
                className="flex-shrink-0"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}