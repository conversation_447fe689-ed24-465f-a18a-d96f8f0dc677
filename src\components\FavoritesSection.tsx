import { Heart } from "lucide-react";
import { ContentRow } from "./ContentRow";
import { continueWatchingData, moviesData } from "@/data/mockData";

export function FavoritesSection() {
  const favoriteItems = [
    ...continueWatchingData,
    ...moviesData.slice(0, 3),
  ];

  return (
    <div className="space-y-8">
      {favoriteItems.length > 0 ? (
        <>
          <ContentRow
            title="المفضلة"
            items={favoriteItems}
            size="medium"
          />
          
          <ContentRow
            title="قائمة المشاهدة"
            items={favoriteItems.slice(0, 4)}
            size="medium"
          />
        </>
      ) : (
        <div className="flex flex-col items-center justify-center py-20 text-center">
          <Heart className="h-16 w-16 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold text-foreground mb-2">
            لا توجد عناصر مفضلة
          </h3>
          <p className="text-muted-foreground max-w-sm">
            ابدأ بإضافة أفلامك ومسلسلاتك المفضلة لتظهر هنا
          </p>
        </div>
      )}
    </div>
  );
}