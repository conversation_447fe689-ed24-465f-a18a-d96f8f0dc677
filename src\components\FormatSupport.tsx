import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { CheckCircle, XCircle, AlertTriangle, Monitor } from 'lucide-react';
import Hls from 'hls.js';

interface FormatInfo {
  name: string;
  description: string;
  supported: boolean;
  nativeSupport: boolean;
  librarySupport: boolean;
  recommendation: string;
}

export function FormatSupport() {
  const [formats, setFormats] = useState<FormatInfo[]>([]);
  const [browserInfo, setBrowserInfo] = useState<string>('');

  useEffect(() => {
    const video = document.createElement('video');
    const userAgent = navigator.userAgent;
    
    // Detect browser
    let browser = 'Unknown';
    if (userAgent.includes('Chrome')) browser = 'Chrome';
    else if (userAgent.includes('Firefox')) browser = 'Firefox';
    else if (userAgent.includes('Safari')) browser = 'Safari';
    else if (userAgent.includes('Edge')) browser = 'Edge';
    
    setBrowserInfo(browser);

    const formatTests: FormatInfo[] = [
      {
        name: 'HLS (M3U8)',
        description: 'HTTP Live Streaming - الأكثر شيوعاً في IPTV',
        supported: false,
        nativeSupport: !!video.canPlayType('application/vnd.apple.mpegurl'),
        librarySupport: Hls.isSupported(),
        recommendation: 'موصى به للـ IPTV'
      },
      {
        name: 'MP4',
        description: 'ملفات فيديو عادية',
        supported: !!video.canPlayType('video/mp4'),
        nativeSupport: !!video.canPlayType('video/mp4'),
        librarySupport: false,
        recommendation: 'مدعوم في جميع المتصفحات'
      },
      {
        name: 'WebM',
        description: 'تنسيق ويب مفتوح المصدر',
        supported: !!video.canPlayType('video/webm'),
        nativeSupport: !!video.canPlayType('video/webm'),
        librarySupport: false,
        recommendation: 'جيد للويب'
      },
      {
        name: 'DASH (MPD)',
        description: 'Dynamic Adaptive Streaming',
        supported: false, // Would need dash.js
        nativeSupport: false,
        librarySupport: false, // Not implemented yet
        recommendation: 'يحتاج مكتبة إضافية'
      },
      {
        name: 'RTMP',
        description: 'Real-Time Messaging Protocol',
        supported: false,
        nativeSupport: false,
        librarySupport: false,
        recommendation: 'غير مدعوم في المتصفحات الحديثة'
      },
      {
        name: 'RTSP',
        description: 'Real Time Streaming Protocol',
        supported: false,
        nativeSupport: false,
        librarySupport: false,
        recommendation: 'غير مدعوم في المتصفحات'
      }
    ];

    // Update HLS support
    formatTests[0].supported = formatTests[0].nativeSupport || formatTests[0].librarySupport;

    setFormats(formatTests);
  }, []);

  const getSupportIcon = (format: FormatInfo) => {
    if (format.supported) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    } else if (format.name.includes('RTMP') || format.name.includes('RTSP')) {
      return <XCircle className="h-4 w-4 text-red-500" />;
    } else {
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getSupportBadge = (format: FormatInfo) => {
    if (format.supported) {
      return <Badge variant="default" className="bg-green-500">مدعوم</Badge>;
    } else if (format.name.includes('RTMP') || format.name.includes('RTSP')) {
      return <Badge variant="destructive">غير مدعوم</Badge>;
    } else {
      return <Badge variant="secondary">محدود</Badge>;
    }
  };

  const getSupportDetails = (format: FormatInfo) => {
    const details = [];
    
    if (format.nativeSupport) {
      details.push('دعم أصلي من المتصفح');
    }
    
    if (format.librarySupport) {
      details.push('دعم عبر مكتبة خارجية');
    }
    
    if (!format.nativeSupport && !format.librarySupport) {
      details.push('غير مدعوم حالياً');
    }
    
    return details.join(' + ');
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Monitor className="h-5 w-5" />
            <span>دعم تنسيقات الفيديو</span>
          </CardTitle>
          <CardDescription>
            تحقق من التنسيقات المدعومة في متصفحك ({browserInfo})
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {formats.map((format, index) => (
              <div 
                key={index}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="flex items-center space-x-3 flex-1">
                  {getSupportIcon(format)}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">{format.name}</h4>
                      {getSupportBadge(format)}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {format.description}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {getSupportDetails(format)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-muted-foreground">
                    {format.recommendation}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>نصائح للحصول على أفضل دعم</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm">
          <div>
            <h4 className="font-medium text-green-600">✅ للحصول على أفضل تجربة:</h4>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground mt-2">
              <li>استخدم روابط HLS (.m3u8) - الأكثر دعماً</li>
              <li>متصفح Chrome أو Firefox للحصول على أفضل أداء</li>
              <li>تأكد من تفعيل JavaScript</li>
              <li>استخدم اتصال إنترنت سريع</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-yellow-600">⚠️ تجنب:</h4>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground mt-2">
              <li>روابط RTMP/RTSP (غير مدعومة في المتصفحات)</li>
              <li>تنسيقات غير شائعة</li>
              <li>روابط تتطلب plugins خاصة</li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-blue-600">💡 إذا لم يعمل البث:</h4>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground mt-2">
              <li>تحقق من نوع التنسيق أعلاه</li>
              <li>جرب متصفح آخر</li>
              <li>استخدم VPN إذا كان البث محجوب</li>
              <li>تأكد من صحة رابط البث</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
