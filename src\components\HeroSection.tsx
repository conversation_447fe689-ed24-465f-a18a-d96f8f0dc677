import { Play, Info } from "lucide-react";
import { Button } from "./ui/button";
import heroBanner from "@/assets/hero-banner.jpg";

export function HeroSection() {
  return (
    <section className="relative h-[60vh] overflow-hidden">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{ backgroundImage: `url(${heroBanner})` }}
      >
        <div className="absolute inset-0 bg-gradient-to-t from-background via-background/40 to-transparent" />
      </div>

      {/* Content */}
      <div className="relative h-full flex flex-col justify-end p-6 text-white">
        <div className="max-w-2xl space-y-4">
          <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/20 backdrop-blur-sm border border-primary/30">
            <span className="text-sm font-medium text-primary-glow">🔴 بث مباشر</span>
          </div>
          
          <h1 className="text-4xl font-bold leading-tight">
            Premier League Live
          </h1>
          
          <p className="text-lg text-white/90 leading-relaxed">
            Manchester United vs Arsenal • مباراة اليوم
          </p>

          <div className="flex items-center space-x-4 pt-4">
            <Button variant="hero" size="lg" className="space-x-2">
              <Play className="h-5 w-5" />
              <span>مشاهدة الآن</span>
            </Button>
            
            <Button variant="glassy" size="lg" className="space-x-2">
              <Info className="h-5 w-5" />
              <span>معلومات أكثر</span>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}