import React, { useRef, useEffect, useState } from 'react';
import Hls from 'hls.js';
import { Play, Pause, Volume2, VolumeX, Maximize, Minimize, RotateCcw } from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { <PERSON>lider } from './ui/slider';
import { Badge } from './ui/badge';
import { cn } from '@/lib/utils';

interface IPTVPlayerProps {
  src: string;
  title?: string;
  poster?: string;
  autoPlay?: boolean;
  className?: string;
}

export function IPTVPlayer({ 
  src, 
  title, 
  poster, 
  autoPlay = false, 
  className 
}: IPTVPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState([100]);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [streamType, setStreamType] = useState<string>('unknown');
  const [quality, setQuality] = useState<string>('auto');

  // Detect stream type
  const detectStreamType = (url: string): string => {
    if (url.includes('.m3u8')) return 'HLS';
    if (url.includes('.mpd')) return 'DASH';
    if (url.includes('rtmp://')) return 'RTMP';
    if (url.includes('rtsp://')) return 'RTSP';
    if (url.includes('.mp4')) return 'MP4';
    if (url.includes('.webm')) return 'WebM';
    return 'Unknown';
  };

  // Initialize player
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !src) return;

    setIsLoading(true);
    setError(null);
    const type = detectStreamType(src);
    setStreamType(type);

    // Clean up previous HLS instance
    if (hlsRef.current) {
      hlsRef.current.destroy();
      hlsRef.current = null;
    }

    // Handle HLS streams
    if (type === 'HLS' && Hls.isSupported()) {
      const hls = new Hls({
        enableWorker: true,
        lowLatencyMode: true,
        backBufferLength: 90,
        maxBufferLength: 30,
        maxMaxBufferLength: 60,
        maxBufferSize: 60 * 1000 * 1000,
        maxBufferHole: 0.5,
        highBufferWatchdogPeriod: 2,
        nudgeOffset: 0.1,
        nudgeMaxRetry: 3,
        maxFragLookUpTolerance: 0.25,
        liveSyncDurationCount: 3,
        liveMaxLatencyDurationCount: 10,
        liveDurationInfinity: true,
        xhrSetup: (xhr: XMLHttpRequest, url: string) => {
          xhr.setRequestHeader('User-Agent', 'Mozilla/5.0 IPTV Player');
          xhr.timeout = 10000;
        }
      });

      hlsRef.current = hls;

      hls.loadSource(src);
      hls.attachMedia(video);

      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS manifest parsed successfully');
        setIsLoading(false);
        if (autoPlay) {
          video.play().catch(console.error);
        }
      });

      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS Error:', data);
        
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              setError('خطأ في الشبكة. تحقق من الاتصال أو جرب VPN.');
              hls.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              setError('خطأ في الوسائط. جرب إعادة التحميل.');
              hls.recoverMediaError();
              break;
            default:
              setError('خطأ في تشغيل البث المباشر. جرب رابط آخر.');
              break;
          }
        }
      });

      hls.on(Hls.Events.LEVEL_LOADED, (event, data) => {
        if (data.details.live) {
          setDuration(Infinity); // Live stream
        }
      });

    } else if (type === 'HLS' && video.canPlayType('application/vnd.apple.mpegurl')) {
      // Native HLS support (Safari)
      video.src = src;
      setIsLoading(false);
    } else {
      // Direct video source
      video.src = src;
      setIsLoading(false);
    }

    // Video event listeners
    const handleLoadedMetadata = () => {
      setDuration(video.duration);
      setIsLoading(false);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
    };

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleWaiting = () => setIsLoading(true);
    const handleCanPlay = () => setIsLoading(false);
    
    const handleError = () => {
      setIsLoading(false);
      const errorCode = video.error?.code;
      let errorMessage = 'خطأ في تشغيل الفيديو. ';
      
      switch (errorCode) {
        case MediaError.MEDIA_ERR_ABORTED:
          errorMessage += 'تم إلغاء التحميل.';
          break;
        case MediaError.MEDIA_ERR_NETWORK:
          errorMessage += 'خطأ في الشبكة. جرب VPN.';
          break;
        case MediaError.MEDIA_ERR_DECODE:
          errorMessage += 'خطأ في فك التشفير.';
          break;
        case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
          errorMessage += `تنسيق ${type} غير مدعوم في هذا المتصفح.`;
          break;
        default:
          errorMessage += 'خطأ غير معروف.';
      }
      
      setError(errorMessage);
    };

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('waiting', handleWaiting);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('error', handleError);

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('waiting', handleWaiting);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('error', handleError);
      
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, [src, autoPlay]);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play().catch((err) => {
        console.error('Play error:', err);
        if (err.name === 'NotSupportedError') {
          setError(`تنسيق ${streamType} غير مدعوم. جرب متصفح آخر أو رابط مختلف.`);
        } else if (err.name === 'NotAllowedError') {
          setError('التشغيل التلقائي محظور. اضغط للتشغيل.');
        } else {
          setError('فشل في تشغيل الفيديو. تحقق من الرابط.');
        }
      });
    }
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !video.muted;
    setIsMuted(video.muted);
  };

  const handleVolumeChange = (value: number[]) => {
    const video = videoRef.current;
    if (!video) return;

    const newVolume = value[0];
    video.volume = newVolume / 100;
    setVolume([newVolume]);
    
    if (newVolume === 0) {
      setIsMuted(true);
      video.muted = true;
    } else if (isMuted) {
      setIsMuted(false);
      video.muted = false;
    }
  };

  const handleSeek = (value: number[]) => {
    const video = videoRef.current;
    if (!video || !isFinite(duration)) return;

    const newTime = (value[0] / 100) * duration;
    video.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const toggleFullscreen = () => {
    const video = videoRef.current;
    if (!video) return;

    if (!isFullscreen) {
      if (video.requestFullscreen) {
        video.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  const handleReload = () => {
    const video = videoRef.current;
    if (!video) return;

    setError(null);
    setIsLoading(true);
    
    if (hlsRef.current) {
      hlsRef.current.destroy();
      hlsRef.current = null;
    }
    
    // Trigger re-initialization
    video.load();
    
    // Re-run the effect
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  const formatTime = (time: number) => {
    if (!isFinite(time)) return 'مباشر';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const progress = isFinite(duration) && duration > 0 ? (currentTime / duration) * 100 : 0;

  if (error) {
    return (
      <div className={cn("relative bg-black rounded-lg overflow-hidden", className)}>
        <div className="flex items-center justify-center h-64 text-white p-6">
          <div className="text-center space-y-4">
            <p className="text-lg mb-2">⚠️ خطأ في التشغيل</p>
            <p className="text-sm text-gray-300 mb-4">{error}</p>
            
            <div className="space-y-2">
              <Badge variant="outline" className="text-white border-white">
                نوع البث: {streamType}
              </Badge>
              
              <div className="text-xs text-gray-400 space-y-1">
                <p>💡 نصائح للحل:</p>
                <p>• جرب متصفح Chrome أو Firefox</p>
                <p>• استخدم VPN إذا كان البث محجوب</p>
                <p>• تأكد من صحة رابط البث</p>
                <p>• جرب إعادة التحميل</p>
              </div>
            </div>
            
            <div className="flex space-x-2 justify-center">
              <Button 
                variant="outline" 
                className="text-white border-white hover:bg-white hover:text-black"
                onClick={handleReload}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                إعادة المحاولة
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={cn("relative bg-black rounded-lg overflow-hidden group", className)}
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >
      <video
        ref={videoRef}
        poster={poster}
        className="w-full h-full object-cover"
        onClick={togglePlay}
        playsInline
        controls={false}
      />

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p className="text-sm">جاري التحميل...</p>
            <Badge variant="outline" className="mt-2 text-white border-white">
              {streamType}
            </Badge>
          </div>
        </div>
      )}

      {/* Controls Overlay */}
      <div className={cn(
        "absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent",
        "transition-opacity duration-300",
        showControls ? "opacity-100" : "opacity-0"
      )}>
        {/* Title & Stream Info */}
        {title && (
          <div className="absolute top-4 left-4 right-4">
            <h3 className="text-white text-lg font-semibold truncate">{title}</h3>
            <div className="flex items-center space-x-2 mt-1">
              <Badge variant="outline" className="text-white border-white text-xs">
                {streamType}
              </Badge>
              {isFinite(duration) ? (
                <Badge variant="outline" className="text-white border-white text-xs">
                  مسجل
                </Badge>
              ) : (
                <Badge variant="destructive" className="text-xs">
                  مباشر
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Play/Pause Button */}
        <div className="absolute inset-0 flex items-center justify-center">
          <Button
            variant="ghost"
            size="lg"
            onClick={togglePlay}
            className="text-white hover:bg-white/20 h-16 w-16 rounded-full"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            ) : isPlaying ? (
              <Pause className="h-8 w-8" />
            ) : (
              <Play className="h-8 w-8 ml-1" />
            )}
          </Button>
        </div>

        {/* Bottom Controls */}
        <div className="absolute bottom-0 left-0 right-0 p-4 space-y-2">
          {/* Progress Bar - Only for non-live streams */}
          {isFinite(duration) && (
            <div className="flex items-center space-x-2 text-white text-sm">
              <span>{formatTime(currentTime)}</span>
              <div className="flex-1">
                <Slider
                  value={[progress]}
                  onValueChange={handleSeek}
                  max={100}
                  step={0.1}
                  className="w-full"
                />
              </div>
              <span>{formatTime(duration)}</span>
            </div>
          )}

          {/* Control Buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={togglePlay}
                className="text-white hover:bg-white/20"
              >
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={toggleMute}
                className="text-white hover:bg-white/20"
              >
                {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
              </Button>

              <div className="w-20">
                <Slider
                  value={volume}
                  onValueChange={handleVolumeChange}
                  max={100}
                  step={1}
                />
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleReload}
                className="text-white hover:bg-white/20"
                title="إعادة تحميل"
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={toggleFullscreen}
              className="text-white hover:bg-white/20"
            >
              {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
