import { ContentRow } from "./ContentRow";
import { liveChannelsData } from "@/data/mockData";

export function LiveTVSection() {
  return (
    <div className="space-y-8">
      <ContentRow
        title="القنوات المباشرة"
        items={liveChannelsData}
        size="medium"
      />
      
      <ContentRow
        title="القنوات الرياضية"
        items={[...liveChannelsData].map(item => ({ ...item, id: item.id + "-sports" }))}
        size="medium"
      />
      
      <ContentRow
        title="القنوات الإخبارية"
        items={[...liveChannelsData].map(item => ({ ...item, id: item.id + "-news" }))}
        size="medium"
      />
    </div>
  );
}