import { ContentRow } from "./ContentRow";
import { useIPTVManager, Channel } from "@/hooks/useIPTVManager";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Button } from "./ui/button";
import { Settings, RefreshCw } from "lucide-react";
import { useState } from "react";
import { ChannelPlayer } from "./ChannelPlayer";

export function LiveTVSection() {
  const {
    playlists,
    loading,
    error,
    getAllChannels,
    getChannelsByCategory,
    getCategories
  } = useIPTVManager();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState<Channel | null>(null);

  const allChannels = getAllChannels();
  const categories = getCategories();

  // Convert channels to the format expected by ContentRow
  const convertChannelsToItems = (channels: Channel[]) => {
    return channels.map(channel => ({
      id: channel.id,
      title: channel.name,
      subtitle: channel.category,
      image: channel.logo || "/placeholder-channel.jpg",
      type: "live" as const,
      url: channel.url,
      onPlay: () => setSelectedChannel(channel)
    }));
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // Refresh all enabled playlists
      for (const playlist of playlists.filter(p => p.enabled)) {
        // This would trigger a refresh - you might want to add this to useIPTVManager
        console.log(`Refreshing playlist: ${playlist.name}`);
      }
    } finally {
      setRefreshing(false);
    }
  };

  if (playlists.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <Settings className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <CardTitle>لا توجد قوائم تشغيل</CardTitle>
            <CardDescription>
              يرجى إضافة قائمة تشغيل IPTV من الإعدادات لعرض القنوات
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" onClick={() => window.location.hash = '#settings'}>
              <Settings className="h-4 w-4 mr-2" />
              الذهاب إلى الإعدادات
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (allChannels.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <RefreshCw className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <CardTitle>لا توجد قنوات</CardTitle>
            <CardDescription>
              {error ? error : "لم يتم العثور على قنوات في قوائم التشغيل المفعلة"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              className="w-full"
              onClick={handleRefresh}
              disabled={refreshing || loading}
            >
              {refreshing ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              {refreshing ? 'جاري التحديث...' : 'تحديث القنوات'}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-8">
        {/* All Channels */}
        <ContentRow
          title={`جميع القنوات (${allChannels.length})`}
          items={convertChannelsToItems(allChannels.slice(0, 20))}
          size="medium"
        />

        {/* Categories */}
        {categories.slice(0, 5).map(category => {
          const categoryChannels = getChannelsByCategory(category);
          if (categoryChannels.length === 0) return null;

          return (
            <ContentRow
              key={category}
              title={`${category} (${categoryChannels.length})`}
              items={convertChannelsToItems(categoryChannels.slice(0, 20))}
              size="medium"
            />
          );
        })}

        {/* Refresh Button */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing || loading}
          >
            {refreshing ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            {refreshing ? 'جاري التحديث...' : 'تحديث جميع القنوات'}
          </Button>
        </div>
      </div>

      {/* Channel Player Modal */}
      {selectedChannel && (
        <ChannelPlayer
          channel={selectedChannel}
          onClose={() => setSelectedChannel(null)}
        />
      )}
    </>
  );
}