import { Filter } from "lucide-react";
import { Button } from "./ui/button";
import { ContentRow } from "./ContentRow";
import { moviesData, categoriesData } from "@/data/mockData";

export function MoviesSection() {
  return (
    <div className="space-y-8">
      {/* Genre Filter */}
      <div className="px-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">الأنواع</h2>
          <Button variant="ghost" size="sm" className="space-x-2">
            <Filter className="h-4 w-4" />
            <span>فرز</span>
          </Button>
        </div>
        
        <div className="flex space-x-3 overflow-x-auto scrollbar-hide pb-2">
          {categoriesData.map((category) => (
            <Button
              key={category.id}
              variant="nav"
              size="sm"
              className="flex-shrink-0 space-x-2"
            >
              <span>{category.name}</span>
              <span className="text-xs text-muted-foreground">({category.count})</span>
            </Button>
          ))}
        </div>
      </div>

      <ContentRow
        title="أفلام جديدة"
        items={moviesData}
        size="medium"
      />
      
      <ContentRow
        title="الأكثر مشاهدة"
        items={[...moviesData].reverse().map(item => ({ ...item, id: item.id + "-popular" }))}
        size="medium"
      />
      
      <ContentRow
        title="أفلام الحركة"
        items={moviesData.filter(item => item.subtitle?.includes("Action"))}
        size="medium"
      />
    </div>
  );
}