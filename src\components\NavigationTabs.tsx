import { useState } from "react";
import { Button } from "./ui/button";
import { cn } from "@/lib/utils";

interface NavigationTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const tabs = [
  { id: "live-tv", label: "بث مباشر", labelEn: "Live TV" },
  { id: "movies", label: "أفلام", labelEn: "Movies" },
  { id: "series", label: "مسلسلات", labelEn: "Series" },
  { id: "favorites", label: "المفضلة", labelEn: "Favorites" },
];

export function NavigationTabs({ activeTab, onTabChange }: NavigationTabsProps) {
  return (
    <div className="flex items-center justify-center px-4 py-2 bg-muted/30">
      <div className="flex items-center space-x-2 bg-muted rounded-lg p-1">
        {tabs.map((tab) => (
          <Button
            key={tab.id}
            variant={activeTab === tab.id ? "default" : "ghost"}
            size="sm"
            onClick={() => onTabChange(tab.id)}
            className={cn(
              "px-6 py-2 rounded-md transition-all duration-300",
              activeTab === tab.id
                ? "bg-gradient-primary text-primary-foreground shadow-primary"
                : "text-muted-foreground hover:text-foreground"
            )}
          >
            {tab.labelEn}
          </Button>
        ))}
      </div>
    </div>
  );
}