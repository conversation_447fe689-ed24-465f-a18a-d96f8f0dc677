import React, { useState, useEffect } from 'react';
import { Badge } from './ui/badge';
import { Wifi, WifiOff, AlertTriangle } from 'lucide-react';

interface NetworkStatusProps {
  className?: string;
}

export function NetworkStatus({ className }: NetworkStatusProps) {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionType, setConnectionType] = useState<string>('unknown');

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Get connection info if available
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      setConnectionType(connection?.effectiveType || 'unknown');
      
      const handleConnectionChange = () => {
        setConnectionType(connection?.effectiveType || 'unknown');
      };
      
      connection?.addEventListener('change', handleConnectionChange);
      
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
        connection?.removeEventListener('change', handleConnectionChange);
      };
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const getStatusColor = () => {
    if (!isOnline) return 'destructive';
    if (connectionType === 'slow-2g' || connectionType === '2g') return 'secondary';
    if (connectionType === '3g') return 'default';
    return 'default';
  };

  const getStatusText = () => {
    if (!isOnline) return 'غير متصل';
    
    switch (connectionType) {
      case 'slow-2g':
        return 'اتصال بطيء جداً';
      case '2g':
        return 'اتصال بطيء';
      case '3g':
        return 'اتصال متوسط';
      case '4g':
        return 'اتصال سريع';
      default:
        return 'متصل';
    }
  };

  const getIcon = () => {
    if (!isOnline) return <WifiOff className="h-3 w-3" />;
    if (connectionType === 'slow-2g' || connectionType === '2g') {
      return <AlertTriangle className="h-3 w-3" />;
    }
    return <Wifi className="h-3 w-3" />;
  };

  return (
    <Badge 
      variant={getStatusColor()} 
      className={`flex items-center space-x-1 ${className}`}
    >
      {getIcon()}
      <span className="text-xs">{getStatusText()}</span>
    </Badge>
  );
}
