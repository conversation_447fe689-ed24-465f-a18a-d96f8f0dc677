import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Settings, Save, Upload, Download, Trash2, Shield, Eye, EyeOff, Plus, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useIPTVManager } from '@/hooks/useIPTVManager';
import { ConnectionTest } from './ConnectionTest';

interface PlaylistConfig {
  name: string;
  type: 'M3U' | 'M3U8' | 'Xtream' | 'Stalker';
  url: string;
  username?: string;
  password?: string;
  macAddress?: string;
  enabled: boolean;
}

export function SettingsPage() {
  const { toast } = useToast();
  const {
    playlists,
    loading,
    error,
    addPlaylist,
    removePlaylist,
    updatePlaylist,
    getAllChannels,
    getCategories
  } = useIPTVManager();

  const [currentPlaylist, setCurrentPlaylist] = useState<PlaylistConfig>({
    name: '',
    type: 'M3U',
    url: '',
    username: '',
    password: '',
    macAddress: '',
    enabled: true
  });

  // Player Settings
  const [playerSettings, setPlayerSettings] = useState({
    autoPlay: true,
    bufferSize: '5',
    videoQuality: 'auto',
    subtitlesEnabled: true,
    aspectRatio: '16:9'
  });

  // Parental Control
  const [parentalControl, setParentalControl] = useState({
    enabled: false,
    pin: '',
    restrictedCategories: [] as string[]
  });

  // App Settings
  const [appSettings, setAppSettings] = useState({
    darkMode: true,
    language: 'ar',
    cacheDuration: '24',
    updateInterval: '6'
  });

  const handleAddPlaylist = async () => {
    if (!currentPlaylist.name || !currentPlaylist.url) {
      toast({
        title: "خطأ",
        description: "يرجى ملء الحقول المطلوبة",
        variant: "destructive"
      });
      return;
    }

    // Validate required fields based on type
    if (currentPlaylist.type === 'Xtream' && (!currentPlaylist.username || !currentPlaylist.password)) {
      toast({
        title: "خطأ",
        description: "اسم المستخدم وكلمة المرور مطلوبان لـ Xtream",
        variant: "destructive"
      });
      return;
    }

    if (currentPlaylist.type === 'Stalker' && !currentPlaylist.macAddress) {
      toast({
        title: "خطأ",
        description: "عنوان MAC مطلوب لـ Stalker",
        variant: "destructive"
      });
      return;
    }

    try {
      await addPlaylist(currentPlaylist);

      setCurrentPlaylist({
        name: '',
        type: 'M3U',
        url: '',
        username: '',
        password: '',
        macAddress: '',
        enabled: true
      });

      toast({
        title: "تم الحفظ",
        description: "تم إضافة قائمة التشغيل بنجاح"
      });
    } catch (err) {
      toast({
        title: "خطأ",
        description: err instanceof Error ? err.message : "فشل في إضافة قائمة التشغيل",
        variant: "destructive"
      });
    }
  };

  const handleRemovePlaylist = (id: string) => {
    removePlaylist(id);
    toast({
      title: "تم الحذف",
      description: "تم حذف قائمة التشغيل"
    });
  };

  const handleUpdatePlaylist = async (id: string) => {
    try {
      await updatePlaylist(id);
      toast({
        title: "تم التحديث",
        description: "تم تحديث قائمة التشغيل بنجاح"
      });
    } catch (err) {
      toast({
        title: "خطأ",
        description: err instanceof Error ? err.message : "فشل في تحديث قائمة التشغيل",
        variant: "destructive"
      });
    }
  };

  const exportSettings = () => {
    const settings = {
      playlists,
      playerSettings,
      parentalControl,
      appSettings
    };
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'iptv-settings.json';
    link.click();
  };

  const clearCache = () => {
    localStorage.clear();
    toast({
      title: "تم مسح الذاكرة المؤقتة",
      description: "تم مسح جميع البيانات المحفوظة"
    });
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-3 mb-6">
          <Settings className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold text-foreground">الإعدادات</h1>
        </div>

        <Tabs defaultValue="playlists" className="space-y-6">
          <TabsList className="grid grid-cols-5 lg:w-[700px]">
            <TabsTrigger value="playlists">قوائم التشغيل</TabsTrigger>
            <TabsTrigger value="connection">اختبار الاتصال</TabsTrigger>
            <TabsTrigger value="player">المشغل</TabsTrigger>
            <TabsTrigger value="parental">الرقابة الأبوية</TabsTrigger>
            <TabsTrigger value="app">التطبيق</TabsTrigger>
          </TabsList>

          {/* Playlists Tab */}
          <TabsContent value="playlists" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>إضافة قائمة تشغيل جديدة</CardTitle>
                <CardDescription>
                  أضف قوائم التشغيل من مصادر مختلفة (M3U, M3U8, Xtream, Stalker)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">اسم قائمة التشغيل</Label>
                    <Input
                      id="name"
                      value={currentPlaylist.name}
                      onChange={(e) => setCurrentPlaylist({ ...currentPlaylist, name: e.target.value })}
                      placeholder="مثال: IPTV الأساسي"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="type">نوع قائمة التشغيل</Label>
                    <Select
                      value={currentPlaylist.type}
                      onValueChange={(value: string) => setCurrentPlaylist({ ...currentPlaylist, type: value as 'M3U' | 'M3U8' | 'Xtream' | 'Stalker' })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="M3U">M3U</SelectItem>
                        <SelectItem value="M3U8">M3U8</SelectItem>
                        <SelectItem value="Xtream">Xtream Codes</SelectItem>
                        <SelectItem value="Stalker">Stalker Portal</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="url">رابط الخادم</Label>
                  <Input
                    id="url"
                    value={currentPlaylist.url}
                    onChange={(e) => setCurrentPlaylist({ ...currentPlaylist, url: e.target.value })}
                    placeholder="http://example.com:8080"
                  />
                </div>

                {(currentPlaylist.type === 'Xtream' || currentPlaylist.type === 'Stalker') && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="username">اسم المستخدم</Label>
                      <Input
                        id="username"
                        value={currentPlaylist.username}
                        onChange={(e) => setCurrentPlaylist({ ...currentPlaylist, username: e.target.value })}
                        placeholder="اسم المستخدم"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password">كلمة المرور</Label>
                      <Input
                        id="password"
                        type="password"
                        value={currentPlaylist.password}
                        onChange={(e) => setCurrentPlaylist({ ...currentPlaylist, password: e.target.value })}
                        placeholder="كلمة المرور"
                      />
                    </div>
                  </div>
                )}

                {currentPlaylist.type === 'Stalker' && (
                  <div className="space-y-2">
                    <Label htmlFor="mac">عنوان MAC</Label>
                    <Input
                      id="mac"
                      value={currentPlaylist.macAddress}
                      onChange={(e) => setCurrentPlaylist({ ...currentPlaylist, macAddress: e.target.value })}
                      placeholder="00:1A:79:XX:XX:XX"
                    />
                  </div>
                )}

                <Button onClick={handleAddPlaylist} className="w-full" disabled={loading}>
                  {loading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Plus className="h-4 w-4 mr-2" />
                  )}
                  {loading ? 'جاري الإضافة...' : 'إضافة قائمة التشغيل'}
                </Button>

                {error && (
                  <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                    <p className="text-sm text-destructive">{error}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Existing Playlists */}
            {playlists.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>قوائم التشغيل المحفوظة</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {playlists.map((playlist) => (
                      <div key={playlist.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex-1">
                          <h3 className="font-medium">{playlist.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {playlist.type} - {playlist.channels.length} قناة
                          </p>
                          <p className="text-xs text-muted-foreground">
                            آخر تحديث: {playlist.lastUpdated.toLocaleString('ar')}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleUpdatePlaylist(playlist.id)}
                            disabled={loading}
                          >
                            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                          </Button>
                          <Switch checked={playlist.enabled} />
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleRemovePlaylist(playlist.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))
                    }
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Connection Test Tab */}
          <TabsContent value="connection" className="space-y-6">
            <ConnectionTest />
          </TabsContent>

          {/* Player Settings Tab */}
          <TabsContent value="player" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات المشغل</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <Label htmlFor="autoplay">التشغيل التلقائي</Label>
                  <Switch
                    id="autoplay"
                    checked={playerSettings.autoPlay}
                    onCheckedChange={(checked) => setPlayerSettings({ ...playerSettings, autoPlay: checked })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="buffer">حجم التخزين المؤقت (ثواني)</Label>
                  <Select
                    value={playerSettings.bufferSize}
                    onValueChange={(value) => setPlayerSettings({ ...playerSettings, bufferSize: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="3">3 ثواني</SelectItem>
                      <SelectItem value="5">5 ثواني</SelectItem>
                      <SelectItem value="10">10 ثواني</SelectItem>
                      <SelectItem value="15">15 ثانية</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="quality">جودة الفيديو</Label>
                  <Select
                    value={playerSettings.videoQuality}
                    onValueChange={(value) => setPlayerSettings({ ...playerSettings, videoQuality: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">تلقائي</SelectItem>
                      <SelectItem value="1080p">1080p</SelectItem>
                      <SelectItem value="720p">720p</SelectItem>
                      <SelectItem value="480p">480p</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="subtitles">تفعيل الترجمة</Label>
                  <Switch
                    id="subtitles"
                    checked={playerSettings.subtitlesEnabled}
                    onCheckedChange={(checked) => setPlayerSettings({ ...playerSettings, subtitlesEnabled: checked })}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Parental Control Tab */}
          <TabsContent value="parental" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  الرقابة الأبوية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <Label htmlFor="parental-enabled">تفعيل الرقابة الأبوية</Label>
                  <Switch
                    id="parental-enabled"
                    checked={parentalControl.enabled}
                    onCheckedChange={(checked) => setParentalControl({ ...parentalControl, enabled: checked })}
                  />
                </div>

                {parentalControl.enabled && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="pin">رقم PIN</Label>
                      <Input
                        id="pin"
                        type="password"
                        value={parentalControl.pin}
                        onChange={(e) => setParentalControl({ ...parentalControl, pin: e.target.value })}
                        placeholder="أدخل رقم PIN (4 أرقام)"
                        maxLength={4}
                      />
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <Label>الفئات المحظورة</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {['أفلام للكبار', 'أفلام رعب', 'محتوى عنيف', 'برامج حوارية'].map((category) => (
                          <div key={category} className="flex items-center space-x-2">
                            <Switch />
                            <Label className="text-sm">{category}</Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* App Settings Tab */}
          <TabsContent value="app" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات التطبيق</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <Label htmlFor="dark-mode">الوضع الليلي</Label>
                  <Switch
                    id="dark-mode"
                    checked={appSettings.darkMode}
                    onCheckedChange={(checked) => setAppSettings({ ...appSettings, darkMode: checked })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="language">اللغة</Label>
                  <Select
                    value={appSettings.language}
                    onValueChange={(value) => setAppSettings({ ...appSettings, language: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ar">العربية</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="fr">Français</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cache">مدة الذاكرة المؤقتة (ساعات)</Label>
                  <Select
                    value={appSettings.cacheDuration}
                    onValueChange={(value) => setAppSettings({ ...appSettings, cacheDuration: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="6">6 ساعات</SelectItem>
                      <SelectItem value="12">12 ساعة</SelectItem>
                      <SelectItem value="24">24 ساعة</SelectItem>
                      <SelectItem value="48">48 ساعة</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">إدارة البيانات</h3>

                  <div className="flex gap-2">
                    <Button onClick={exportSettings} variant="outline" className="flex-1">
                      <Download className="h-4 w-4 mr-2" />
                      تصدير الإعدادات
                    </Button>

                    <Button onClick={clearCache} variant="destructive" className="flex-1">
                      <Trash2 className="h-4 w-4 mr-2" />
                      مسح الذاكرة المؤقتة
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
