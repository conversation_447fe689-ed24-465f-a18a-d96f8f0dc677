import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Alert, AlertDescription } from './ui/alert';
import { 
  Shield, 
  Globe, 
  Wifi, 
  ExternalLink, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

interface VPNService {
  name: string;
  type: 'free' | 'paid';
  description: string;
  website: string;
  pros: string[];
  cons: string[];
  recommended: boolean;
}

export function VPNHelper() {
  const [userLocation, setUserLocation] = useState<string>('غير معروف');
  const [isVPNDetected, setIsVPNDetected] = useState<boolean | null>(null);
  const [connectionTest, setConnectionTest] = useState<'idle' | 'testing' | 'success' | 'failed'>('idle');

  const vpnServices: VPNService[] = [
    {
      name: 'ProtonVPN',
      type: 'free',
      description: 'خدمة VPN مجانية موثوقة من سويسرا',
      website: 'https://protonvpn.com',
      pros: ['مجاني بالكامل', 'لا حدود للبيانات', 'خصوصية عالية', 'سهل الاستخدام'],
      cons: ['سرعة محدودة', '3 مواقع فقط', 'جهاز واحد فقط'],
      recommended: true
    },
    {
      name: 'Windscribe',
      type: 'free',
      description: 'VPN مجاني مع 10GB شهرياً',
      website: 'https://windscribe.com',
      pros: ['10GB مجاني شهرياً', 'مواقع متعددة', 'حماية إعلانات', 'سهل التثبيت'],
      cons: ['حد البيانات', 'سرعة متوسطة'],
      recommended: true
    },
    {
      name: 'TunnelBear',
      type: 'free',
      description: 'VPN بسيط وسهل الاستخدام',
      website: 'https://tunnelbear.com',
      pros: ['واجهة بسيطة', 'موثوق', 'دعم جيد'],
      cons: ['500MB فقط مجاناً', 'محدود جداً'],
      recommended: false
    },
    {
      name: 'ExpressVPN',
      type: 'paid',
      description: 'VPN مدفوع سريع وموثوق',
      website: 'https://expressvpn.com',
      pros: ['سرعة عالية جداً', 'مواقع كثيرة', 'دعم ممتاز', 'يعمل مع Netflix'],
      cons: ['مدفوع', 'سعر مرتفع'],
      recommended: true
    }
  ];

  useEffect(() => {
    // Detect user location and VPN
    detectLocationAndVPN();
  }, []);

  const detectLocationAndVPN = async () => {
    try {
      // Try to get location info
      const response = await fetch('https://ipapi.co/json/');
      const data = await response.json();
      
      if (data.country_name) {
        setUserLocation(`${data.city}, ${data.country_name}`);
        
        // Simple VPN detection (not 100% accurate)
        const isVPN = data.org?.toLowerCase().includes('vpn') || 
                     data.org?.toLowerCase().includes('proxy') ||
                     data.asn?.toString().includes('vpn');
        setIsVPNDetected(isVPN);
      }
    } catch (error) {
      console.warn('Could not detect location:', error);
      setUserLocation('غير متاح');
    }
  };

  const testConnection = async () => {
    setConnectionTest('testing');
    
    try {
      // Test a known working URL
      const testUrl = 'https://httpbin.org/get';
      const response = await fetch(testUrl, { 
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        setConnectionTest('success');
      } else {
        setConnectionTest('failed');
      }
    } catch (error) {
      setConnectionTest('failed');
    }
    
    // Reset after 3 seconds
    setTimeout(() => setConnectionTest('idle'), 3000);
  };

  const getVPNStatusIcon = () => {
    if (isVPNDetected === null) return <RefreshCw className="h-4 w-4 animate-spin" />;
    if (isVPNDetected) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getVPNStatusText = () => {
    if (isVPNDetected === null) return 'جاري الفحص...';
    if (isVPNDetected) return 'VPN مفعل';
    return 'VPN غير مفعل';
  };

  const getConnectionIcon = () => {
    switch (connectionTest) {
      case 'testing':
        return <RefreshCw className="h-4 w-4 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Wifi className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Current Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>حالة الاتصال الحالية</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="text-sm font-medium">الموقع:</span>
              <span className="text-sm">{userLocation}</span>
            </div>
            
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="text-sm font-medium">VPN:</span>
              <div className="flex items-center space-x-2">
                {getVPNStatusIcon()}
                <span className="text-sm">{getVPNStatusText()}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-center">
              <Button 
                variant="outline" 
                size="sm"
                onClick={testConnection}
                disabled={connectionTest === 'testing'}
              >
                {getConnectionIcon()}
                <span className="ml-2">
                  {connectionTest === 'testing' ? 'جاري الاختبار...' : 
                   connectionTest === 'success' ? 'الاتصال جيد' :
                   connectionTest === 'failed' ? 'مشكلة في الاتصال' : 'اختبار الاتصال'}
                </span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* VPN Recommendation Alert */}
      {!isVPNDetected && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>نصيحة:</strong> إذا كنت تواجه مشاكل في الوصول لروابط IPTV، 
            جرب استخدام VPN لتغيير موقعك الجغرافي.
          </AlertDescription>
        </Alert>
      )}

      {/* VPN Services */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {vpnServices.map((service, index) => (
          <Card key={index} className={service.recommended ? 'border-primary' : ''}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5" />
                  <span>{service.name}</span>
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Badge variant={service.type === 'free' ? 'default' : 'secondary'}>
                    {service.type === 'free' ? 'مجاني' : 'مدفوع'}
                  </Badge>
                  {service.recommended && (
                    <Badge variant="outline" className="border-primary text-primary">
                      موصى به
                    </Badge>
                  )}
                </div>
              </div>
              <CardDescription>{service.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Pros */}
              <div>
                <h4 className="text-sm font-medium text-green-600 mb-2">✅ المميزات:</h4>
                <ul className="text-xs space-y-1">
                  {service.pros.map((pro, i) => (
                    <li key={i} className="flex items-center space-x-1">
                      <span>•</span>
                      <span>{pro}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Cons */}
              <div>
                <h4 className="text-sm font-medium text-red-600 mb-2">❌ العيوب:</h4>
                <ul className="text-xs space-y-1">
                  {service.cons.map((con, i) => (
                    <li key={i} className="flex items-center space-x-1">
                      <span>•</span>
                      <span>{con}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Website Link */}
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full"
                onClick={() => window.open(service.website, '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                زيارة الموقع
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>كيفية استخدام VPN لحل مشاكل IPTV</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3 text-sm">
            <div>
              <h4 className="font-medium">1. اختر خدمة VPN:</h4>
              <p className="text-muted-foreground">
                للاستخدام المجاني، ننصح بـ ProtonVPN أو Windscribe
              </p>
            </div>

            <div>
              <h4 className="font-medium">2. قم بالتسجيل والتثبيت:</h4>
              <p className="text-muted-foreground">
                سجل حساب مجاني وحمل التطبيق على جهازك
              </p>
            </div>

            <div>
              <h4 className="font-medium">3. اختر موقع مناسب:</h4>
              <p className="text-muted-foreground">
                جرب مواقع مختلفة مثل: الولايات المتحدة، ألمانيا، هولندا
              </p>
            </div>

            <div>
              <h4 className="font-medium">4. اختبر الاتصال:</h4>
              <p className="text-muted-foreground">
                بعد تفعيل VPN، استخدم "اختبار الاتصال" في التطبيق
              </p>
            </div>

            <div>
              <h4 className="font-medium">5. جرب إضافة قائمة IPTV:</h4>
              <p className="text-muted-foreground">
                الآن يجب أن تعمل الروابط التي كانت محجوبة سابقاً
              </p>
            </div>
          </div>

          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>ملاحظة أمنية:</strong> استخدم VPN موثوق فقط ولا تشارك معلومات حساسة أثناء استخدامه.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
}
