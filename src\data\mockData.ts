import moviePoster1 from "@/assets/movie-poster-1.jpg";
import moviePoster2 from "@/assets/movie-poster-2.jpg";
import moviePoster3 from "@/assets/movie-poster-3.jpg";

export const continueWatchingData = [
  {
    id: "1",
    title: "The Dark Knight",
    subtitle: "01:25:30 left",
    image: moviePoster3,
    duration: "152 min",
    rating: 9.0,
    year: "2008",
    type: "movie" as const,
  },
  {
    id: "2",
    title: "Interstellar",
    subtitle: "45:12 left",
    image: moviePoster2,
    duration: "169 min",
    rating: 8.6,
    year: "2014",
    type: "movie" as const,
  },
  {
    id: "3",
    title: "Inception",
    subtitle: "30:45 left",
    image: moviePoster1,
    duration: "148 min",
    rating: 8.8,
    year: "2010",
    type: "movie" as const,
  },
];

export const moviesData = [
  {
    id: "4",
    title: "The Last Stand",
    subtitle: "Action • 2023",
    image: moviePoster1,
    duration: "125 min",
    rating: 7.8,
    year: "2023",
    type: "movie" as const,
  },
  {
    id: "5",
    title: "Cyber Revolution",
    subtitle: "Sci-Fi • 2023",
    image: moviePoster2,
    duration: "140 min",
    rating: 8.2,
    year: "2023",
    type: "movie" as const,
  },
  {
    id: "6",
    title: "Wild War",
    subtitle: "War • 2023",
    image: moviePoster3,
    duration: "118 min",
    rating: 7.5,
    year: "2023",
    type: "movie" as const,
  },
  {
    id: "7",
    title: "Shadow Hunter",
    subtitle: "Thriller • 2023",
    image: moviePoster1,
    duration: "132 min",
    rating: 8.1,
    year: "2023",
    type: "movie" as const,
  },
  {
    id: "8",
    title: "Neon City",
    subtitle: "Action • 2023",
    image: moviePoster2,
    duration: "145 min",
    rating: 7.9,
    year: "2023",
    type: "movie" as const,
  },
];

export const seriesData = [
  {
    id: "9",
    title: "Breaking Bad",
    subtitle: "5 Seasons • Crime",
    image: moviePoster3,
    duration: "47 episodes",
    rating: 9.5,
    year: "2008",
    type: "series" as const,
  },
  {
    id: "10",
    title: "Stranger Things",
    subtitle: "4 Seasons • Sci-Fi",
    image: moviePoster2,
    duration: "42 episodes",
    rating: 8.7,
    year: "2016",
    type: "series" as const,
  },
  {
    id: "11",
    title: "The Crown",
    subtitle: "6 Seasons • Drama",
    image: moviePoster1,
    duration: "60 episodes",
    rating: 8.6,
    year: "2016",
    type: "series" as const,
  },
];

export const liveChannelsData = [
  {
    id: "12",
    title: "BBC Sport",
    subtitle: "Premier League Live",
    image: moviePoster1,
    type: "live" as const,
  },
  {
    id: "13",
    title: "CNN News",
    subtitle: "Breaking News",
    image: moviePoster2,
    type: "live" as const,
  },
  {
    id: "14",
    title: "Discovery",
    subtitle: "Wildlife Documentary",
    image: moviePoster3,
    type: "live" as const,
  },
];

export const categoriesData = [
  { id: "action", name: "Action", count: 45 },
  { id: "comedy", name: "Comedy", count: 32 },
  { id: "drama", name: "Drama", count: 28 },
  { id: "horror", name: "Horror", count: 19 },
  { id: "sci-fi", name: "Sci-Fi", count: 24 },
  { id: "romance", name: "Romance", count: 16 },
];