import { useState, useEffect } from 'react';

export interface Channel {
  id: string;
  name: string;
  logo?: string;
  url: string;
  category: string;
  epg?: string;
  isAdult?: boolean;
}

export interface PlaylistData {
  id: string;
  name: string;
  type: 'M3U' | 'M3U8' | 'Xtream' | 'Stalker';
  url: string;
  username?: string;
  password?: string;
  macAddress?: string;
  channels: Channel[];
  enabled: boolean;
  lastUpdated: Date;
}

interface XtreamInfo {
  username: string;
  password: string;
  message: string;
  auth: number;
  status: string;
  exp_date: string;
  is_trial: string;
  active_cons: string;
  created_at: string;
  max_connections: string;
  allowed_output_formats: string[];
}

export const useIPTVManager = () => {
  const [playlists, setPlaylists] = useState<PlaylistData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load playlists from localStorage on mount
  useEffect(() => {
    const savedPlaylists = localStorage.getItem('iptv-playlists');
    if (savedPlaylists) {
      try {
        setPlaylists(JSON.parse(savedPlaylists));
      } catch (err) {
        console.error('Error loading playlists:', err);
      }
    }
  }, []);

  // Save playlists to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('iptv-playlists', JSON.stringify(playlists));
  }, [playlists]);

  const parseM3U = (content: string): Channel[] => {
    const lines = content.split('\n');
    const channels: Channel[] = [];
    let currentChannel: Partial<Channel> = {};

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (line.startsWith('#EXTINF:')) {
        // Parse channel info
        const info = line.substring(8);
        const nameMatch = info.match(/,(.+)$/);
        const logoMatch = info.match(/tvg-logo="([^"]+)"/);
        const categoryMatch = info.match(/group-title="([^"]+)"/);
        const epgMatch = info.match(/tvg-id="([^"]+)"/);
        
        currentChannel = {
          id: Date.now().toString() + Math.random(),
          name: nameMatch ? nameMatch[1].trim() : 'Unknown Channel',
          logo: logoMatch ? logoMatch[1] : undefined,
          category: categoryMatch ? categoryMatch[1] : 'General',
          epg: epgMatch ? epgMatch[1] : undefined,
          isAdult: info.toLowerCase().includes('adult') || info.toLowerCase().includes('xxx')
        };
      } else if (line && !line.startsWith('#') && currentChannel.name) {
        // This is the URL line
        currentChannel.url = line;
        channels.push(currentChannel as Channel);
        currentChannel = {};
      }
    }

    return channels;
  };

  const fetchXtreamChannels = async (url: string, username: string, password: string): Promise<Channel[]> => {
    try {
      // Get live categories
      const categoriesResponse = await fetch(`${url}/player_api.php?username=${username}&password=${password}&action=get_live_categories`);
      const categories = await categoriesResponse.json();

      // Get live streams
      const streamsResponse = await fetch(`${url}/player_api.php?username=${username}&password=${password}&action=get_live_streams`);
      const streams = await streamsResponse.json();

      const channels: Channel[] = streams.map((stream: any) => ({
        id: stream.stream_id.toString(),
        name: stream.name,
        logo: stream.stream_icon,
        url: `${url}/live/${username}/${password}/${stream.stream_id}.m3u8`,
        category: categories.find((cat: any) => cat.category_id === stream.category_id)?.category_name || 'General',
        epg: stream.epg_channel_id,
        isAdult: stream.is_adult === 1
      }));

      return channels;
    } catch (err) {
      throw new Error('Failed to fetch Xtream channels');
    }
  };

  const fetchStalkerChannels = async (url: string, macAddress: string): Promise<Channel[]> => {
    try {
      // Simulate Stalker Portal authentication
      const authResponse = await fetch(`${url}/stalker_portal/server/load.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-User-Agent': 'Model: MAG254; Link: WiFi'
        },
        body: `type=stb&action=handshake&token=&mac=${macAddress}`
      });

      if (!authResponse.ok) {
        throw new Error('Stalker authentication failed');
      }

      // Get channels list (simplified)
      const channelsResponse = await fetch(`${url}/stalker_portal/server/load.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `type=itv&action=get_all_channels&mac=${macAddress}`
      });

      const data = await channelsResponse.json();
      
      const channels: Channel[] = data.js.data.map((channel: any) => ({
        id: channel.id.toString(),
        name: channel.name,
        logo: channel.logo,
        url: channel.cmd,
        category: channel.tv_genre_name || 'General',
        isAdult: channel.censored === 1
      }));

      return channels;
    } catch (err) {
      throw new Error('Failed to fetch Stalker channels');
    }
  };

  const addPlaylist = async (config: Omit<PlaylistData, 'id' | 'channels' | 'lastUpdated'>) => {
    setLoading(true);
    setError(null);

    try {
      let channels: Channel[] = [];

      switch (config.type) {
        case 'M3U':
        case 'M3U8':
          const response = await fetch(config.url);
          if (!response.ok) throw new Error('Failed to fetch M3U playlist');
          const content = await response.text();
          channels = parseM3U(content);
          break;

        case 'Xtream':
          if (!config.username || !config.password) {
            throw new Error('Username and password required for Xtream');
          }
          channels = await fetchXtreamChannels(config.url, config.username, config.password);
          break;

        case 'Stalker':
          if (!config.macAddress) {
            throw new Error('MAC address required for Stalker');
          }
          channels = await fetchStalkerChannels(config.url, config.macAddress);
          break;
      }

      const newPlaylist: PlaylistData = {
        ...config,
        id: Date.now().toString(),
        channels,
        lastUpdated: new Date()
      };

      setPlaylists(prev => [...prev, newPlaylist]);
      return newPlaylist;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add playlist';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const removePlaylist = (id: string) => {
    setPlaylists(prev => prev.filter(p => p.id !== id));
  };

  const updatePlaylist = async (id: string) => {
    const playlist = playlists.find(p => p.id === id);
    if (!playlist) return;

    setLoading(true);
    try {
      let channels: Channel[] = [];

      switch (playlist.type) {
        case 'M3U':
        case 'M3U8':
          const response = await fetch(playlist.url);
          const content = await response.text();
          channels = parseM3U(content);
          break;

        case 'Xtream':
          channels = await fetchXtreamChannels(playlist.url, playlist.username!, playlist.password!);
          break;

        case 'Stalker':
          channels = await fetchStalkerChannels(playlist.url, playlist.macAddress!);
          break;
      }

      setPlaylists(prev => prev.map(p => 
        p.id === id 
          ? { ...p, channels, lastUpdated: new Date() }
          : p
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update playlist');
    } finally {
      setLoading(false);
    }
  };

  const getAllChannels = (): Channel[] => {
    return playlists
      .filter(p => p.enabled)
      .flatMap(p => p.channels);
  };

  const getChannelsByCategory = (category: string): Channel[] => {
    return getAllChannels().filter(c => c.category === category);
  };

  const getCategories = (): string[] => {
    const categories = new Set(getAllChannels().map(c => c.category));
    return Array.from(categories).sort();
  };

  return {
    playlists,
    loading,
    error,
    addPlaylist,
    removePlaylist,
    updatePlaylist,
    getAllChannels,
    getChannelsByCategory,
    getCategories
  };
};