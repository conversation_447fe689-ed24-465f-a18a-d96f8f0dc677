
import { useState, useEffect } from 'react';

export interface Channel {
  id: string;
  name: string;
  logo?: string;
  url: string;
  category: string;
  epg?: string;
  isAdult?: boolean;
}

export interface PlaylistData {
  id: string;
  name: string;
  type: 'M3U' | 'M3U8' | 'Xtream' | 'Stalker';
  url: string;
  username?: string;
  password?: string;
  macAddress?: string;
  channels: Channel[];
  enabled: boolean;
  lastUpdated: Date;
}

interface XtreamInfo {
  username: string;
  password: string;
  message: string;
  auth: number;
  status: string;
  exp_date: string;
  is_trial: string;
  active_cons: string;
  created_at: string;
  max_connections: string;
  allowed_output_formats: string[];
}

export const useIPTVManager = () => {
  const [playlists, setPlaylists] = useState<PlaylistData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load playlists from localStorage on mount
  useEffect(() => {
    const savedPlaylists = localStorage.getItem('iptv-playlists');
    if (savedPlaylists) {
      try {
        setPlaylists(JSON.parse(savedPlaylists));
      } catch (err) {
        console.error('Error loading playlists:', err);
      }
    }
  }, []);

  // Save playlists to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('iptv-playlists', JSON.stringify(playlists));
  }, [playlists]);

  const parseM3U = (content: string): Channel[] => {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);
    const channels: Channel[] = [];
    let currentChannel: Partial<Channel> = {};

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (line.startsWith('#EXTINF:')) {
        // Parse channel info
        const info = line.substring(8);
        const nameMatch = info.match(/,(.+)$/);
        const logoMatch = info.match(/tvg-logo="([^"]+)"/i);
        const categoryMatch = info.match(/group-title="([^"]+)"/i);
        const epgMatch = info.match(/tvg-id="([^"]+)"/i);
        const tvgNameMatch = info.match(/tvg-name="([^"]+)"/i);

        const channelName = nameMatch ? nameMatch[1].trim() :
                           tvgNameMatch ? tvgNameMatch[1].trim() :
                           'Unknown Channel';

        currentChannel = {
          id: `channel_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          name: channelName,
          logo: logoMatch ? logoMatch[1] : undefined,
          category: categoryMatch ? categoryMatch[1] : 'General',
          epg: epgMatch ? epgMatch[1] : undefined,
          isAdult: info.toLowerCase().includes('adult') ||
                   info.toLowerCase().includes('xxx') ||
                   info.toLowerCase().includes('18+')
        };
      } else if (line && !line.startsWith('#') && currentChannel.name) {
        // This is the URL line
        if (line.startsWith('http') || line.startsWith('rtmp') || line.startsWith('rtsp')) {
          currentChannel.url = line;
          channels.push(currentChannel as Channel);
          currentChannel = {};
        }
      }
    }

    return channels;
  };

  const fetchXtreamChannels = async (url: string, username: string, password: string): Promise<Channel[]> => {
    try {
      // Clean URL
      const baseUrl = url.replace(/\/$/, '');

      // Test authentication first
      const authResponse = await fetch(`${baseUrl}/player_api.php?username=${username}&password=${password}`);
      if (!authResponse.ok) {
        throw new Error('Authentication failed. Check your credentials.');
      }

      const authData = await authResponse.json();
      if (authData.user_info?.auth !== 1) {
        throw new Error('Invalid credentials or expired account.');
      }

      // Get live categories
      const categoriesResponse = await fetch(`${baseUrl}/player_api.php?username=${username}&password=${password}&action=get_live_categories`);
      if (!categoriesResponse.ok) {
        throw new Error('Failed to fetch categories');
      }
      const categories = await categoriesResponse.json();

      // Get live streams
      const streamsResponse = await fetch(`${baseUrl}/player_api.php?username=${username}&password=${password}&action=get_live_streams`);
      if (!streamsResponse.ok) {
        throw new Error('Failed to fetch streams');
      }
      const streams = await streamsResponse.json();

      if (!Array.isArray(streams)) {
        throw new Error('Invalid streams data received');
      }

      const channels: Channel[] = streams.map((stream: {
        stream_id: number;
        name: string;
        stream_icon?: string;
        category_id: string;
        epg_channel_id?: string;
        is_adult?: number;
      }) => ({
        id: stream.stream_id.toString(),
        name: stream.name || 'Unknown Channel',
        logo: stream.stream_icon || undefined,
        url: `${baseUrl}/live/${username}/${password}/${stream.stream_id}.m3u8`,
        category: categories.find((cat: { category_id: string; category_name: string }) =>
          cat.category_id === stream.category_id)?.category_name || 'General',
        epg: stream.epg_channel_id || undefined,
        isAdult: stream.is_adult === 1
      }));

      return channels;
    } catch (err) {
      console.error('Xtream fetch error:', err);
      throw new Error(err instanceof Error ? err.message : 'Failed to fetch Xtream channels');
    }
  };

  const fetchStalkerChannels = async (url: string, macAddress: string): Promise<Channel[]> => {
    try {
      // Clean URL and MAC address
      const baseUrl = url.replace(/\/$/, '');
      const cleanMac = macAddress.replace(/[:-]/g, '').toLowerCase();

      if (!/^[0-9a-f]{12}$/.test(cleanMac)) {
        throw new Error('Invalid MAC address format. Use format: 00:1A:79:XX:XX:XX');
      }

      // Format MAC address properly
      const formattedMac = cleanMac.match(/.{2}/g)?.join(':') || cleanMac;

      // Stalker Portal authentication
      const authResponse = await fetch(`${baseUrl}/stalker_portal/server/load.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-User-Agent': 'Model: MAG254; Link: WiFi',
          'Authorization': 'Bearer'
        },
        body: `type=stb&action=handshake&token=&mac=${formattedMac}`
      });

      if (!authResponse.ok) {
        throw new Error(`Stalker authentication failed: ${authResponse.status}`);
      }

      const authData = await authResponse.json();
      const token = authData.js?.token;

      if (!token) {
        throw new Error('Failed to get authentication token');
      }

      // Get channels list
      const channelsResponse = await fetch(`${baseUrl}/stalker_portal/server/load.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Bearer ${token}`
        },
        body: `type=itv&action=get_all_channels&mac=${formattedMac}&token=${token}`
      });

      if (!channelsResponse.ok) {
        throw new Error(`Failed to fetch channels: ${channelsResponse.status}`);
      }

      const data = await channelsResponse.json();

      if (!data.js?.data || !Array.isArray(data.js.data)) {
        throw new Error('Invalid channel data received');
      }

      const channels: Channel[] = data.js.data.map((channel: {
        id: number;
        name: string;
        logo?: string;
        cmd: string;
        tv_genre_name?: string;
        censored?: number;
      }) => ({
        id: channel.id.toString(),
        name: channel.name || 'Unknown Channel',
        logo: channel.logo || undefined,
        url: channel.cmd,
        category: channel.tv_genre_name || 'General',
        isAdult: channel.censored === 1
      }));

      return channels;
    } catch (err) {
      console.error('Stalker fetch error:', err);
      throw new Error(err instanceof Error ? err.message : 'Failed to fetch Stalker channels');
    }
  };

  const addPlaylist = async (config: Omit<PlaylistData, 'id' | 'channels' | 'lastUpdated'>) => {
    setLoading(true);
    setError(null);

    try {
      let channels: Channel[] = [];

      // Validate URL
      if (!config.url || !config.url.trim()) {
        throw new Error('URL is required');
      }

      switch (config.type) {
        case 'M3U':
        case 'M3U8': {
          const response = await fetch(config.url, {
            method: 'GET',
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch M3U playlist: ${response.status} ${response.statusText}`);
          }

          const content = await response.text();
          if (!content.includes('#EXTM3U')) {
            throw new Error('Invalid M3U file format');
          }

          channels = parseM3U(content);
          if (channels.length === 0) {
            throw new Error('No valid channels found in M3U file');
          }
          break;
        }

        case 'Xtream':
          if (!config.username || !config.password) {
            throw new Error('Username and password are required for Xtream');
          }
          channels = await fetchXtreamChannels(config.url, config.username, config.password);
          break;

        case 'Stalker':
          if (!config.macAddress) {
            throw new Error('MAC address is required for Stalker');
          }
          channels = await fetchStalkerChannels(config.url, config.macAddress);
          break;
      }

      if (channels.length === 0) {
        throw new Error('No channels found in the playlist');
      }

      const newPlaylist: PlaylistData = {
        ...config,
        id: `playlist_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        channels,
        lastUpdated: new Date()
      };

      setPlaylists(prev => [...prev, newPlaylist]);
      return newPlaylist;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add playlist';
      setError(errorMessage);
      console.error('Add playlist error:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const removePlaylist = (id: string) => {
    setPlaylists(prev => prev.filter(p => p.id !== id));
  };

  const updatePlaylist = async (id: string) => {
    const playlist = playlists.find(p => p.id === id);
    if (!playlist) return;

    setLoading(true);
    try {
      let channels: Channel[] = [];

      switch (playlist.type) {
        case 'M3U':
        case 'M3U8': {
          const response = await fetch(playlist.url);
          const content = await response.text();
          channels = parseM3U(content);
          break;
        }

        case 'Xtream':
          channels = await fetchXtreamChannels(playlist.url, playlist.username!, playlist.password!);
          break;

        case 'Stalker':
          channels = await fetchStalkerChannels(playlist.url, playlist.macAddress!);
          break;
      }

      setPlaylists(prev => prev.map(p =>
        p.id === id
          ? { ...p, channels, lastUpdated: new Date() }
          : p
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update playlist');
    } finally {
      setLoading(false);
    }
  };

  const getAllChannels = (): Channel[] => {
    return playlists
      .filter(p => p.enabled)
      .flatMap(p => p.channels);
  };

  const getChannelsByCategory = (category: string): Channel[] => {
    return getAllChannels().filter(c => c.category === category);
  };

  const getCategories = (): string[] => {
    const categories = new Set(getAllChannels().map(c => c.category));
    return Array.from(categories).sort();
  };

  return {
    playlists,
    loading,
    error,
    addPlaylist,
    removePlaylist,
    updatePlaylist,
    getAllChannels,
    getChannelsByCategory,
    getCategories
  };
};



