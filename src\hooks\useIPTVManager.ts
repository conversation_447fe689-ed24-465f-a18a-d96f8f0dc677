
import { useState, useEffect } from 'react';
import { fetchWithCORS, normalizeUrl, getErrorMessage, NetworkError } from '@/utils/corsProxy';

export interface Channel {
  id: string;
  name: string;
  logo?: string;
  url: string;
  category: string;
  epg?: string;
  isAdult?: boolean;
}

export interface PlaylistData {
  id: string;
  name: string;
  type: 'M3U' | 'M3U8' | 'Xtream' | 'Stalker';
  url: string;
  username?: string;
  password?: string;
  macAddress?: string;
  channels: Channel[];
  enabled: boolean;
  lastUpdated: Date;
}

interface XtreamInfo {
  username: string;
  password: string;
  message: string;
  auth: number;
  status: string;
  exp_date: string;
  is_trial: string;
  active_cons: string;
  created_at: string;
  max_connections: string;
  allowed_output_formats: string[];
}

export const useIPTVManager = () => {
  const [playlists, setPlaylists] = useState<PlaylistData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load playlists from localStorage on mount
  useEffect(() => {
    const savedPlaylists = localStorage.getItem('iptv-playlists');
    if (savedPlaylists) {
      try {
        setPlaylists(JSON.parse(savedPlaylists));
      } catch (err) {
        console.error('Error loading playlists:', err);
      }
    }
  }, []);

  // Save playlists to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('iptv-playlists', JSON.stringify(playlists));
  }, [playlists]);

  const parseM3U = (content: string): Channel[] => {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);
    const channels: Channel[] = [];
    let currentChannel: Partial<Channel> = {};

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (line.startsWith('#EXTINF:')) {
        // Parse channel info
        const info = line.substring(8);
        const nameMatch = info.match(/,(.+)$/);
        const logoMatch = info.match(/tvg-logo="([^"]+)"/i);
        const categoryMatch = info.match(/group-title="([^"]+)"/i);
        const epgMatch = info.match(/tvg-id="([^"]+)"/i);
        const tvgNameMatch = info.match(/tvg-name="([^"]+)"/i);

        const channelName = nameMatch ? nameMatch[1].trim() :
                           tvgNameMatch ? tvgNameMatch[1].trim() :
                           'Unknown Channel';

        currentChannel = {
          id: `channel_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          name: channelName,
          logo: logoMatch ? logoMatch[1] : undefined,
          category: categoryMatch ? categoryMatch[1] : 'General',
          epg: epgMatch ? epgMatch[1] : undefined,
          isAdult: info.toLowerCase().includes('adult') ||
                   info.toLowerCase().includes('xxx') ||
                   info.toLowerCase().includes('18+')
        };
      } else if (line && !line.startsWith('#') && currentChannel.name) {
        // This is the URL line
        if (line.startsWith('http') || line.startsWith('rtmp') || line.startsWith('rtsp')) {
          currentChannel.url = line;
          channels.push(currentChannel as Channel);
          currentChannel = {};
        }
      }
    }

    return channels;
  };

  const fetchXtreamChannels = async (url: string, username: string, password: string): Promise<Channel[]> => {
    try {
      // Clean and normalize URL
      const baseUrl = normalizeUrl(url);

      // Test authentication first
      const authUrl = `${baseUrl}/player_api.php?username=${username}&password=${password}`;
      const authResponse = await fetchWithCORS(authUrl, { timeout: 15000 });

      if (!authResponse.ok) {
        throw new NetworkError(`فشل في المصادقة: ${authResponse.status}`, authResponse.status);
      }

      const authData = await authResponse.json();
      if (authData.user_info?.auth !== 1) {
        throw new NetworkError('بيانات اعتماد غير صحيحة أو حساب منتهي الصلاحية');
      }

      // Get live categories
      const categoriesUrl = `${baseUrl}/player_api.php?username=${username}&password=${password}&action=get_live_categories`;
      const categoriesResponse = await fetchWithCORS(categoriesUrl, { timeout: 15000 });

      if (!categoriesResponse.ok) {
        throw new NetworkError('فشل في جلب الفئات');
      }
      const categories = await categoriesResponse.json();

      // Get live streams
      const streamsUrl = `${baseUrl}/player_api.php?username=${username}&password=${password}&action=get_live_streams`;
      const streamsResponse = await fetchWithCORS(streamsUrl, { timeout: 20000 });

      if (!streamsResponse.ok) {
        throw new NetworkError('فشل في جلب القنوات');
      }
      const streams = await streamsResponse.json();

      if (!Array.isArray(streams)) {
        throw new NetworkError('بيانات قنوات غير صحيحة');
      }

      const channels: Channel[] = streams.map((stream: {
        stream_id: number;
        name: string;
        stream_icon?: string;
        category_id: string;
        epg_channel_id?: string;
        is_adult?: number;
      }) => ({
        id: stream.stream_id.toString(),
        name: stream.name || 'قناة غير معروفة',
        logo: stream.stream_icon || undefined,
        url: `${baseUrl}/live/${username}/${password}/${stream.stream_id}.m3u8`,
        category: categories.find((cat: { category_id: string; category_name: string }) =>
          cat.category_id === stream.category_id)?.category_name || 'عام',
        epg: stream.epg_channel_id || undefined,
        isAdult: stream.is_adult === 1
      }));

      return channels;
    } catch (err) {
      console.error('Xtream fetch error:', err);
      throw new NetworkError(getErrorMessage(err));
    }
  };

  const fetchStalkerChannels = async (url: string, macAddress: string): Promise<Channel[]> => {
    try {
      // Clean URL and MAC address
      const baseUrl = normalizeUrl(url);
      const cleanMac = macAddress.replace(/[:-]/g, '').toLowerCase();

      if (!/^[0-9a-f]{12}$/.test(cleanMac)) {
        throw new NetworkError('تنسيق عنوان MAC غير صحيح. استخدم: 00:1A:79:XX:XX:XX');
      }

      // Format MAC address properly
      const formattedMac = cleanMac.match(/.{2}/g)?.join(':') || cleanMac;

      // Stalker Portal authentication
      const authUrl = `${baseUrl}/stalker_portal/server/load.php`;
      const authResponse = await fetchWithCORS(authUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-User-Agent': 'Model: MAG254; Link: WiFi',
        },
        body: `type=stb&action=handshake&token=&mac=${formattedMac}`,
        timeout: 15000
      });

      if (!authResponse.ok) {
        throw new NetworkError(`فشل في مصادقة Stalker: ${authResponse.status}`, authResponse.status);
      }

      const authData = await authResponse.json();
      const token = authData.js?.token;

      if (!token) {
        throw new NetworkError('فشل في الحصول على رمز المصادقة');
      }

      // Get channels list
      const channelsResponse = await fetchWithCORS(authUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=itv&action=get_all_channels&mac=${formattedMac}&token=${token}`,
        timeout: 20000
      });

      if (!channelsResponse.ok) {
        throw new NetworkError(`فشل في جلب القنوات: ${channelsResponse.status}`, channelsResponse.status);
      }

      const data = await channelsResponse.json();

      if (!data.js?.data || !Array.isArray(data.js.data)) {
        throw new NetworkError('بيانات قنوات غير صحيحة');
      }

      const channels: Channel[] = data.js.data.map((channel: {
        id: number;
        name: string;
        logo?: string;
        cmd: string;
        tv_genre_name?: string;
        censored?: number;
      }) => ({
        id: channel.id.toString(),
        name: channel.name || 'قناة غير معروفة',
        logo: channel.logo || undefined,
        url: channel.cmd,
        category: channel.tv_genre_name || 'عام',
        isAdult: channel.censored === 1
      }));

      return channels;
    } catch (err) {
      console.error('Stalker fetch error:', err);
      throw new NetworkError(getErrorMessage(err));
    }
  };

  const addPlaylist = async (config: Omit<PlaylistData, 'id' | 'channels' | 'lastUpdated'>) => {
    setLoading(true);
    setError(null);

    try {
      let channels: Channel[] = [];

      // Validate URL
      if (!config.url || !config.url.trim()) {
        throw new NetworkError('الرابط مطلوب');
      }

      const normalizedUrl = normalizeUrl(config.url);

      switch (config.type) {
        case 'M3U':
        case 'M3U8': {
          const response = await fetchWithCORS(normalizedUrl, {
            timeout: 20000,
            retries: 2
          });

          if (!response.ok) {
            throw new NetworkError(`فشل في جلب قائمة M3U: ${response.status}`, response.status);
          }

          const content = await response.text();
          if (!content.includes('#EXTM3U')) {
            throw new NetworkError('تنسيق ملف M3U غير صحيح');
          }

          channels = parseM3U(content);
          if (channels.length === 0) {
            throw new NetworkError('لم يتم العثور على قنوات صحيحة في ملف M3U');
          }
          break;
        }

        case 'Xtream':
          if (!config.username || !config.password) {
            throw new NetworkError('اسم المستخدم وكلمة المرور مطلوبان لـ Xtream');
          }
          channels = await fetchXtreamChannels(normalizedUrl, config.username, config.password);
          break;

        case 'Stalker':
          if (!config.macAddress) {
            throw new NetworkError('عنوان MAC مطلوب لـ Stalker');
          }
          channels = await fetchStalkerChannels(normalizedUrl, config.macAddress);
          break;
      }

      if (channels.length === 0) {
        throw new NetworkError('لم يتم العثور على قنوات في قائمة التشغيل');
      }

      const newPlaylist: PlaylistData = {
        ...config,
        url: normalizedUrl, // Use normalized URL
        id: `playlist_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        channels,
        lastUpdated: new Date()
      };

      setPlaylists(prev => [...prev, newPlaylist]);
      return newPlaylist;
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      console.error('Add playlist error:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const removePlaylist = (id: string) => {
    setPlaylists(prev => prev.filter(p => p.id !== id));
  };

  const updatePlaylist = async (id: string) => {
    const playlist = playlists.find(p => p.id === id);
    if (!playlist) return;

    setLoading(true);
    try {
      let channels: Channel[] = [];

      switch (playlist.type) {
        case 'M3U':
        case 'M3U8': {
          const response = await fetch(playlist.url);
          const content = await response.text();
          channels = parseM3U(content);
          break;
        }

        case 'Xtream':
          channels = await fetchXtreamChannels(playlist.url, playlist.username!, playlist.password!);
          break;

        case 'Stalker':
          channels = await fetchStalkerChannels(playlist.url, playlist.macAddress!);
          break;
      }

      setPlaylists(prev => prev.map(p =>
        p.id === id
          ? { ...p, channels, lastUpdated: new Date() }
          : p
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update playlist');
    } finally {
      setLoading(false);
    }
  };

  const getAllChannels = (): Channel[] => {
    return playlists
      .filter(p => p.enabled)
      .flatMap(p => p.channels);
  };

  const getChannelsByCategory = (category: string): Channel[] => {
    return getAllChannels().filter(c => c.category === category);
  };

  const getCategories = (): string[] => {
    const categories = new Set(getAllChannels().map(c => c.category));
    return Array.from(categories).sort();
  };

  return {
    playlists,
    loading,
    error,
    addPlaylist,
    removePlaylist,
    updatePlaylist,
    getAllChannels,
    getChannelsByCategory,
    getCategories
  };
};



