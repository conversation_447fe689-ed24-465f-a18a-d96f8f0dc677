@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* IPTV App Dark Theme */
    --background: 220 13% 9%;
    --foreground: 0 0% 95%;

    --card: 220 13% 12%;
    --card-foreground: 0 0% 95%;

    --popover: 220 13% 12%;
    --popover-foreground: 0 0% 95%;

    --primary: 263 70% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 263 70% 70%;

    --secondary: 220 13% 16%;
    --secondary-foreground: 0 0% 90%;

    --muted: 220 13% 16%;
    --muted-foreground: 0 0% 60%;

    --accent: 263 70% 60%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 20%;
    --input: 220 13% 16%;
    --ring: 263 70% 60%;

    --radius: 0.75rem;

    /* Custom IPTV Variables */
    --gradient-primary: linear-gradient(135deg, hsl(263, 70%, 60%), hsl(280, 70%, 65%));
    --gradient-secondary: linear-gradient(135deg, hsl(220, 13%, 12%), hsl(220, 13%, 16%));
    --gradient-hero: linear-gradient(135deg, hsl(263, 70%, 60%), hsl(220, 13%, 9%));
    --shadow-card: 0 4px 20px hsl(220, 13%, 5%);
    --shadow-primary: 0 8px 32px hsl(263, 70%, 60%, 0.3);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  /* Custom animation classes */
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 20px hsl(var(--primary) / 0.2);
    }
    to {
      box-shadow: 0 0 30px hsl(var(--primary) / 0.4);
    }
  }
}