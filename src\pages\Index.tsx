import { useState } from "react";
import { Header } from "@/components/Header";
import { NavigationTabs } from "@/components/NavigationTabs";
import { BottomNavigation } from "@/components/BottomNavigation";
import { HeroSection } from "@/components/HeroSection";
import { ContentRow } from "@/components/ContentRow";
import { LiveTVSection } from "@/components/LiveTVSection";
import { MoviesSection } from "@/components/MoviesSection";
import { SeriesSection } from "@/components/SeriesSection";
import { FavoritesSection } from "@/components/FavoritesSection";
import { SettingsPage } from "@/components/SettingsPage";
import { continueWatchingData } from "@/data/mockData";

const Index = () => {
  const [activeTab, setActiveTab] = useState("live-tv");
  const [bottomNavTab, setBottomNavTab] = useState("home");

  const renderContent = () => {
    if (bottomNavTab === "settings") {
      return <SettingsPage />;
    }
    
    switch (activeTab) {
      case "live-tv":
        return <LiveTVSection />;
      case "movies":
        return <MoviesSection />;
      case "series":
        return <SeriesSection />;
      case "favorites":
        return <FavoritesSection />;
      default:
        return <LiveTVSection />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      {bottomNavTab !== "settings" && <Header />}

      {/* Navigation Tabs */}
      {bottomNavTab !== "settings" && <NavigationTabs activeTab={activeTab} onTabChange={setActiveTab} />}

      {/* Main Content */}
      <main className="pb-20">
        {bottomNavTab === "settings" ? (
          renderContent()
        ) : (
          <>
            {/* Hero Section - Only show on Live TV tab */}
            {activeTab === "live-tv" && <HeroSection />}

            {/* Continue Watching - Show on home */}
            {activeTab === "live-tv" && (
              <div className="py-8">
                <ContentRow
                  title="متابعة المشاهدة"
                  items={continueWatchingData}
                  size="medium"
                />
              </div>
            )}

            {/* Dynamic Content */}
            <div className="py-4">
              {renderContent()}
            </div>
          </>
        )}
      </main>

      {/* Bottom Navigation */}
      <BottomNavigation
        activeTab={bottomNavTab}
        onTabChange={setBottomNavTab}
      />
    </div>
  );
};

export default Index;
