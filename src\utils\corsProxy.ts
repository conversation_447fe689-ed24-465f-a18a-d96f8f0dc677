// CORS Proxy utilities for IPTV requests

export interface FetchOptions extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

// List of public CORS proxy services (ordered by reliability)
const CORS_PROXIES = [
  // Most reliable
  'https://api.allorigins.win/raw?url=',
  'https://api.codetabs.com/v1/proxy?quest=',

  // Alternative formats
  'https://corsproxy.io/?',
  'https://cors-anywhere.herokuapp.com/',
  'https://thingproxy.freeboard.io/fetch/',

  // Additional proxies
  'https://proxy.cors.sh/',
  'https://yacdn.org/proxy/',
  'https://api.allorigins.win/get?url=', // JSON format
];

// Backup method using different approach
const ALTERNATIVE_PROXIES = [
  'https://crossorigin.me/',
  'https://cors.bridged.cc/',
  'https://api.codetabs.com/v1/proxy/?quest=',
];

export class NetworkError extends Error {
  constructor(message: string, public status?: number, public url?: string) {
    super(message);
    this.name = 'NetworkError';
  }
}

export async function fetchWithCORS(
  url: string,
  options: FetchOptions = {}
): Promise<Response> {
  const { timeout = 15000, retries = 2, retryDelay = 1000, ...fetchOptions } = options;

  // First try direct fetch
  try {
    const response = await fetchWithTimeout(url, fetchOptions, timeout);
    if (response.ok) {
      console.log('✅ Direct fetch successful for:', url);
      return response;
    }
  } catch (error) {
    console.warn('❌ Direct fetch failed, trying proxies...', error);
  }

  // Try with primary CORS proxies
  for (let attempt = 0; attempt <= retries; attempt++) {
    console.log(`🔄 Attempt ${attempt + 1}/${retries + 1}`);

    for (const proxy of CORS_PROXIES) {
      try {
        let proxyUrl: string;
        let processResponse: (response: Response) => Promise<Response>;

        // Handle different proxy formats
        if (proxy.includes('allorigins.win/get')) {
          // JSON format proxy
          proxyUrl = proxy + encodeURIComponent(url);
          processResponse = async (response) => {
            const data = await response.json();
            if (data.contents) {
              return new Response(data.contents, {
                status: 200,
                statusText: 'OK',
                headers: { 'Content-Type': 'text/plain' }
              });
            }
            throw new Error('No contents in JSON response');
          };
        } else {
          // Raw format proxy
          proxyUrl = proxy + encodeURIComponent(url);
          processResponse = async (response) => response;
        }

        console.log(`🔗 Trying proxy: ${proxy.split('/')[2]}`);

        const response = await fetchWithTimeout(proxyUrl, {
          ...fetchOptions,
          mode: 'cors',
          credentials: 'omit',
          headers: {
            'Accept': '*/*',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ...fetchOptions.headers
          }
        }, timeout);

        if (response.ok) {
          const processedResponse = await processResponse(response);
          console.log(`✅ Proxy successful: ${proxy.split('/')[2]}`);
          return processedResponse;
        }
      } catch (error) {
        console.warn(`❌ Proxy ${proxy.split('/')[2]} failed:`, error);
        continue;
      }
    }

    // Try alternative proxies on last attempt
    if (attempt === retries) {
      console.log('🔄 Trying alternative proxies...');
      for (const proxy of ALTERNATIVE_PROXIES) {
        try {
          const proxyUrl = proxy + encodeURIComponent(url);
          console.log(`🔗 Trying alternative: ${proxy.split('/')[2]}`);

          const response = await fetchWithTimeout(proxyUrl, {
            ...fetchOptions,
            mode: 'cors',
            credentials: 'omit'
          }, timeout / 2); // Shorter timeout for alternatives

          if (response.ok) {
            console.log(`✅ Alternative proxy successful: ${proxy.split('/')[2]}`);
            return response;
          }
        } catch (error) {
          console.warn(`❌ Alternative ${proxy.split('/')[2]} failed:`, error);
          continue;
        }
      }
    }

    // Wait before retry
    if (attempt < retries) {
      console.log(`⏳ Waiting ${retryDelay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  throw new NetworkError(
    'فشل في الوصول للرابط بعد تجربة جميع الطرق المتاحة.\n\n' +
    '💡 الحلول المقترحة:\n' +
    '• استخدم VPN وجرب مرة أخرى\n' +
    '• تحقق من صحة الرابط\n' +
    '• جرب في وقت آخر\n' +
    '• تأكد من اتصال الإنترنت',
    0,
    url
  );
}

async function fetchWithTimeout(
  url: string,
  options: RequestInit,
  timeout: number
): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
        'Cache-Control': 'no-cache',
        ...options.headers,
      }
    });

    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new NetworkError('انتهت مهلة الاتصال', 408, url);
      }
      if (error.message.includes('CORS')) {
        throw new NetworkError('خطأ في سياسة CORS', 0, url);
      }
      if (error.message.includes('network')) {
        throw new NetworkError('خطأ في الشبكة', 0, url);
      }
    }

    throw new NetworkError('فشل في الاتصال', 0, url);
  }
}

// Validate URL format
export function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return ['http:', 'https:', 'rtmp:', 'rtsp:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
}

// Clean and normalize URL
export function normalizeUrl(url: string): string {
  if (!url) return '';

  // Remove extra spaces and trim
  url = url.trim();

  // Add protocol if missing
  if (!url.startsWith('http') && !url.startsWith('rtmp') && !url.startsWith('rtsp')) {
    url = 'http://' + url;
  }

  // Remove trailing slash for base URLs
  if (url.endsWith('/') && !url.includes('m3u')) {
    url = url.slice(0, -1);
  }

  return url;
}

// Test if URL is accessible
export async function testUrl(url: string): Promise<boolean> {
  try {
    const response = await fetchWithCORS(url, {
      method: 'HEAD',
      timeout: 5000,
      retries: 1
    });
    return response.ok;
  } catch {
    return false;
  }
}

// Get error message in Arabic
export function getErrorMessage(error: unknown): string {
  if (error instanceof NetworkError) {
    return error.message;
  }

  if (error instanceof Error) {
    if (error.message.includes('CORS')) {
      return 'خطأ في سياسة CORS. جرب رابط آخر أو استخدم VPN.';
    }
    if (error.message.includes('timeout')) {
      return 'انتهت مهلة الاتصال. تحقق من سرعة الإنترنت.';
    }
    if (error.message.includes('network')) {
      return 'خطأ في الشبكة. تحقق من الاتصال.';
    }
    if (error.message.includes('404')) {
      return 'الرابط غير موجود (404).';
    }
    if (error.message.includes('403')) {
      return 'الوصول مرفوض (403). تحقق من الصلاحيات.';
    }
    if (error.message.includes('401')) {
      return 'مطلوب مصادقة (401). تحقق من بيانات الاعتماد.';
    }
  }

  return 'خطأ غير معروف في الشبكة.';
}
