// CORS Proxy utilities for IPTV requests

export interface FetchOptions extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

// List of public CORS proxy services
const CORS_PROXIES = [
  'https://api.allorigins.win/raw?url=',
  'https://corsproxy.io/?',
  'https://cors-anywhere.herokuapp.com/',
  'https://thingproxy.freeboard.io/fetch/',
  'https://proxy.cors.sh/',
];

export class NetworkError extends Error {
  constructor(message: string, public status?: number, public url?: string) {
    super(message);
    this.name = 'NetworkError';
  }
}

export async function fetchWithCORS(
  url: string,
  options: FetchOptions = {}
): Promise<Response> {
  const { timeout = 15000, retries = 2, retryDelay = 1000, ...fetchOptions } = options;

  // First try direct fetch
  try {
    const response = await fetchWithTimeout(url, fetchOptions, timeout);
    if (response.ok) {
      console.log('Direct fetch successful for:', url);
      return response;
    }
  } catch (error) {
    console.warn('Direct fetch failed, trying proxies...', error);
  }

  // Try with CORS proxies
  const workingProxies = [...CORS_PROXIES];

  for (let attempt = 0; attempt <= retries; attempt++) {
    for (const proxy of workingProxies) {
      try {
        const proxyUrl = proxy + encodeURIComponent(url);
        console.log(`Trying proxy: ${proxy}`);

        const response = await fetchWithTimeout(proxyUrl, {
          ...fetchOptions,
          mode: 'cors',
          credentials: 'omit'
        }, timeout);

        if (response.ok) {
          console.log(`Proxy successful: ${proxy}`);
          return response;
        }
      } catch (error) {
        console.warn(`Proxy ${proxy} failed:`, error);
        continue;
      }
    }

    // Wait before retry
    if (attempt < retries) {
      console.log(`Retrying in ${retryDelay}ms... (attempt ${attempt + 1}/${retries + 1})`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  throw new NetworkError(
    'فشل في الوصول للرابط. جرب استخدام VPN أو رابط آخر.',
    0,
    url
  );
}

async function fetchWithTimeout(
  url: string,
  options: RequestInit,
  timeout: number
): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
        'Cache-Control': 'no-cache',
        ...options.headers,
      }
    });

    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new NetworkError('انتهت مهلة الاتصال', 408, url);
      }
      if (error.message.includes('CORS')) {
        throw new NetworkError('خطأ في سياسة CORS', 0, url);
      }
      if (error.message.includes('network')) {
        throw new NetworkError('خطأ في الشبكة', 0, url);
      }
    }

    throw new NetworkError('فشل في الاتصال', 0, url);
  }
}

// Validate URL format
export function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return ['http:', 'https:', 'rtmp:', 'rtsp:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
}

// Clean and normalize URL
export function normalizeUrl(url: string): string {
  if (!url) return '';

  // Remove extra spaces and trim
  url = url.trim();

  // Add protocol if missing
  if (!url.startsWith('http') && !url.startsWith('rtmp') && !url.startsWith('rtsp')) {
    url = 'http://' + url;
  }

  // Remove trailing slash for base URLs
  if (url.endsWith('/') && !url.includes('m3u')) {
    url = url.slice(0, -1);
  }

  return url;
}

// Test if URL is accessible
export async function testUrl(url: string): Promise<boolean> {
  try {
    const response = await fetchWithCORS(url, {
      method: 'HEAD',
      timeout: 5000,
      retries: 1
    });
    return response.ok;
  } catch {
    return false;
  }
}

// Get error message in Arabic
export function getErrorMessage(error: unknown): string {
  if (error instanceof NetworkError) {
    return error.message;
  }

  if (error instanceof Error) {
    if (error.message.includes('CORS')) {
      return 'خطأ في سياسة CORS. جرب رابط آخر أو استخدم VPN.';
    }
    if (error.message.includes('timeout')) {
      return 'انتهت مهلة الاتصال. تحقق من سرعة الإنترنت.';
    }
    if (error.message.includes('network')) {
      return 'خطأ في الشبكة. تحقق من الاتصال.';
    }
    if (error.message.includes('404')) {
      return 'الرابط غير موجود (404).';
    }
    if (error.message.includes('403')) {
      return 'الوصول مرفوض (403). تحقق من الصلاحيات.';
    }
    if (error.message.includes('401')) {
      return 'مطلوب مصادقة (401). تحقق من بيانات الاعتماد.';
    }
  }

  return 'خطأ غير معروف في الشبكة.';
}
