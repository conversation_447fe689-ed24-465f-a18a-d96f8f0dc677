// Fallback fetch methods for when CORS proxies fail

export interface FallbackOptions {
  timeout?: number;
  userAgent?: string;
}

// Try different approaches when CORS proxies fail
export async function fallbackFetch(url: string, options: FallbackOptions = {}): Promise<string> {
  const { timeout = 10000, userAgent = 'Mozilla/5.0 IPTV Player' } = options;

  // Method 1: Try with different headers
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': userAgent,
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
      },
      mode: 'no-cors', // This might work for some cases
      signal: AbortSignal.timeout(timeout)
    });

    if (response.type === 'opaque') {
      throw new Error('Opaque response - cannot read content');
    }

    return await response.text();
  } catch (error) {
    console.warn('Fallback method 1 failed:', error);
  }

  // Method 2: Try JSONP-like approach for some endpoints
  if (url.includes('.m3u') || url.includes('.m3u8')) {
    try {
      const jsonpUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`;
      const response = await fetch(jsonpUrl, {
        signal: AbortSignal.timeout(timeout)
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.contents) {
          return data.contents;
        }
      }
    } catch (error) {
      console.warn('Fallback method 2 failed:', error);
    }
  }

  // Method 3: Try with different proxy format
  try {
    const proxyUrl = `https://corsproxy.io/?${encodeURIComponent(url)}`;
    const response = await fetch(proxyUrl, {
      signal: AbortSignal.timeout(timeout)
    });
    
    if (response.ok) {
      return await response.text();
    }
  } catch (error) {
    console.warn('Fallback method 3 failed:', error);
  }

  throw new Error('جميع طرق الوصول فشلت. جرب استخدام VPN أو رابط آخر.');
}

// Check if URL might work with fallback methods
export function canUseFallback(url: string): boolean {
  try {
    const urlObj = new URL(url);
    
    // Check if it's a common IPTV format
    const supportedFormats = ['.m3u', '.m3u8', '.xml'];
    const hasFormat = supportedFormats.some(format => 
      urlObj.pathname.includes(format) || urlObj.search.includes(format)
    );
    
    // Check if it's HTTP/HTTPS
    const isHttp = ['http:', 'https:'].includes(urlObj.protocol);
    
    return isHttp && hasFormat;
  } catch {
    return false;
  }
}

// Generate helpful error message with suggestions
export function getFallbackErrorMessage(url: string, originalError: string): string {
  let message = `فشل في الوصول للرابط: ${originalError}\n\n`;
  
  message += 'الحلول المقترحة:\n';
  message += '• استخدم VPN وجرب مرة أخرى\n';
  message += '• تحقق من صحة الرابط\n';
  message += '• جرب في وقت آخر (قد يكون الخادم مشغول)\n';
  
  if (url.includes('localhost') || url.includes('127.0.0.1')) {
    message += '• الرابط محلي - تأكد من تشغيل الخادم\n';
  }
  
  if (!url.startsWith('https://')) {
    message += '• جرب استخدام HTTPS بدلاً من HTTP\n';
  }
  
  return message;
}

// Test multiple URLs and return the first working one
export async function testMultipleUrls(urls: string[]): Promise<{ url: string; content: string } | null> {
  for (const url of urls) {
    try {
      console.log(`Testing URL: ${url}`);
      const content = await fallbackFetch(url, { timeout: 5000 });
      
      if (content && content.length > 0) {
        console.log(`URL working: ${url}`);
        return { url, content };
      }
    } catch (error) {
      console.warn(`URL failed: ${url}`, error);
      continue;
    }
  }
  
  return null;
}
